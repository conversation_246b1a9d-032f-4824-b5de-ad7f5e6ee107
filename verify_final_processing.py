#!/usr/bin/env python3
"""
验证最终处理结果
"""

import pandas as pd
import json

def verify_final_processing():
    """验证最终处理结果"""
    try:
        print("="*80)
        print("验证最终处理结果")
        print("="*80)
        
        # 读取处理后的文件
        df = pd.read_excel('games_list_processed.xlsx')
        
        print(f"✅ 文件读取成功")
        print(f"📊 基本信息:")
        print(f"   - 总行数: {len(df):,}")
        print(f"   - 总列数: {len(df.columns)}")
        
        # 1. 验证Tag列
        print(f"\n🏷️  Tag列验证:")
        if 'tag' in df.columns:
            comma_format = 0
            multiline_format = 0
            empty_tags = 0
            
            for tag in df['tag']:
                if pd.isna(tag) or tag == '':
                    empty_tags += 1
                elif ',' in str(tag) and '\n' not in str(tag):
                    comma_format += 1
                elif '\n' in str(tag):
                    multiline_format += 1
            
            print(f"   逗号分隔格式: {comma_format}")
            print(f"   多行格式: {multiline_format}")
            print(f"   空标签: {empty_tags}")
            
            # 显示样本
            print(f"   Tag样本:")
            count = 0
            for tag in df['tag']:
                if pd.notna(tag) and tag != '' and count < 3:
                    print(f"     {count+1}. {tag}")
                    count += 1
        
        # 2. 验证简介列
        print(f"\n📝 简介列验证:")
        if '简介' in df.columns:
            html_format = 0
            markdown_format = 0
            empty_intros = 0
            json_compatible = 0
            
            for intro in df['简介']:
                if pd.isna(intro) or intro == '':
                    empty_intros += 1
                else:
                    intro_str = str(intro)
                    
                    # 检查是否还是HTML格式
                    if '<' in intro_str and '>' in intro_str:
                        html_format += 1
                    else:
                        markdown_format += 1
                    
                    # 测试JSON兼容性
                    try:
                        json.dumps({"test": intro_str})
                        json_compatible += 1
                    except:
                        pass
            
            print(f"   HTML格式: {html_format}")
            print(f"   Markdown格式: {markdown_format}")
            print(f"   空简介: {empty_intros}")
            print(f"   JSON兼容: {json_compatible}")
            
            # 显示样本
            print(f"   简介样本:")
            count = 0
            for intro in df['简介']:
                if pd.notna(intro) and intro != '' and count < 2:
                    sample = str(intro)[:150] + "..." if len(str(intro)) > 150 else str(intro)
                    print(f"     {count+1}. {sample}")
                    count += 1
        
        # 3. 验证Video列
        print(f"\n🎬 Video列验证:")
        if 'video' in df.columns:
            exists_count = 0
            empty_count = 0
            other_count = 0
            
            for video in df['video']:
                if pd.isna(video) or video == '':
                    empty_count += 1
                elif str(video) == '存在':
                    exists_count += 1
                else:
                    other_count += 1
            
            print(f"   标记为'存在': {exists_count}")
            print(f"   空白: {empty_count}")
            print(f"   其他值: {other_count}")
            print(f"   视频覆盖率: {exists_count/len(df)*100:.1f}%")
            
            # 显示有视频的游戏样本
            print(f"   有视频的游戏样本:")
            count = 0
            for idx, row in df.iterrows():
                if str(row['video']) == '存在' and count < 3:
                    print(f"     {count+1}. {row['标题']}")
                    count += 1
        
        # 4. JSON兼容性测试
        print(f"\n🧪 JSON兼容性测试:")
        
        # 选择前2条记录进行JSON测试
        test_records = []
        for i in range(min(2, len(df))):
            record = {}
            for col in df.columns:
                value = df.iloc[i][col]
                if pd.isna(value):
                    record[col] = None
                else:
                    record[col] = str(value)
            test_records.append(record)
        
        try:
            json_str = json.dumps(test_records, ensure_ascii=False, indent=2)
            print(f"   ✅ JSON序列化成功")
            print(f"   JSON长度: {len(json_str):,} 字符")
            
        except Exception as e:
            print(f"   ❌ JSON序列化失败: {e}")
        
        print(f"\n✅ 验证完成！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_final_processing()
