#!/usr/bin/env python3
"""
根据新的URL规则更新video列中失败或空白的URL
"""

import pandas as pd
import os
from urllib.parse import urlparse

def extract_game_slug_from_url(url):
    """从URL中提取游戏slug"""
    if pd.isna(url) or not url:
        return None
    
    try:
        path = urlparse(url).path
        game_slug = path.strip('/').split('/')[-1]
        return game_slug if game_slug else None
    except:
        return None

def generate_new_video_url(title_url):
    """根据新规则生成video URL"""
    game_slug = extract_game_slug_from_url(title_url)
    if not game_slug:
        return None

    # 新的URL格式: https://1games.io/data/file/game/{game_slug}.mp4
    new_video_url = f"https://1games.io/data/file/game/{game_slug}.mp4"
    return new_video_url

def should_update_video_url(video_url, video_status):
    """判断是否需要更新video URL"""
    # 如果是本地文件，不更新
    if isinstance(video_url, str) and video_url.startswith('videos/'):
        return False
    
    # 如果video_status包含失败信息，需要更新
    if isinstance(video_status, str):
        if '下载失败' in video_status or '404' in video_status:
            return True
        if '无URL' in video_status or '无效URL' in video_status:
            return True
    
    # 如果video_url为空或无效，需要更新
    if pd.isna(video_url) or not video_url:
        return True
    
    # 如果是旧的cdn.1games.io格式，需要更新
    if isinstance(video_url, str) and 'cdn.1games.io' in video_url:
        return True
    
    return False

def update_failed_video_urls():
    """更新失败或空白的video URL"""
    
    print("="*80)
    print("更新Video URL程序 - 新规则")
    print("="*80)
    
    # 读取Excel文件
    try:
        df = pd.read_excel('merged_games.xlsx')
        print(f"读取Excel文件成功: {len(df)} 个游戏")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 分析当前状态
    print(f"\n📊 当前video状态分析:")
    
    # 统计需要更新的记录
    need_update_count = 0
    local_file_count = 0
    success_count = 0
    
    for idx, row in df.iterrows():
        video_url = row.get('video', '')
        video_status = row.get('video_download_status', '')
        
        if isinstance(video_url, str) and video_url.startswith('videos/'):
            local_file_count += 1
        elif should_update_video_url(video_url, video_status):
            need_update_count += 1
        else:
            success_count += 1
    
    print(f"   - 本地文件: {local_file_count}")
    print(f"   - 需要更新: {need_update_count}")
    print(f"   - 其他状态: {success_count}")
    
    # 显示新URL规则
    print(f"\n🔧 新URL规则:")
    print(f"   旧格式: https://cdn.1games.io/file/game/{{game-slug}}/{{game-slug}}.mp4")
    print(f"   新格式: https://1games.io/data/file/game/{{game-slug}}.mp4")
    
    # 显示示例
    print(f"\n📝 URL转换示例:")
    examples = [
        "cuby-road-halloween",
        "xmas-dash",
        "santa-run",
        "doodle-basketball"
    ]
    
    for slug in examples:
        old_url = f"https://cdn.1games.io/file/game/{slug}/{slug}.mp4"
        new_url = f"https://1games.io/data/file/game/{slug}.mp4"
        print(f"   {slug}:")
        print(f"     旧: {old_url}")
        print(f"     新: {new_url}")
    
    # 开始更新
    print(f"\n🔄 开始更新video URL...")
    
    updated_count = 0
    skipped_count = 0
    error_count = 0
    
    # 添加新的状态列
    if 'video_url_updated' not in df.columns:
        df['video_url_updated'] = ''
    
    for idx, row in df.iterrows():
        title = row['标题']
        title_url = row['标题链接']
        video_url = row.get('video', '')
        video_status = row.get('video_download_status', '')
        
        # 检查是否需要更新
        if not should_update_video_url(video_url, video_status):
            df.at[idx, 'video_url_updated'] = '无需更新'
            skipped_count += 1
            continue
        
        # 生成新的video URL
        new_video_url = generate_new_video_url(title_url)
        
        if new_video_url:
            df.at[idx, 'video'] = new_video_url
            df.at[idx, 'video_url_updated'] = '已更新为新格式'
            df.at[idx, 'video_download_status'] = ''  # 清空旧的下载状态
            updated_count += 1
            
            if updated_count <= 10:  # 只显示前10个更新示例
                print(f"   ✅ {title}")
                print(f"      新URL: {new_video_url}")
        else:
            df.at[idx, 'video_url_updated'] = '无法生成URL'
            error_count += 1
            
            if error_count <= 5:  # 只显示前5个错误示例
                print(f"   ❌ {title}: 无法从标题链接生成URL")
    
    # 保存更新后的Excel文件
    try:
        df.to_excel('merged_games.xlsx', index=False)
        print(f"\n✅ Excel文件已更新")
    except Exception as e:
        print(f"❌ 保存Excel文件失败: {e}")
        return
    
    # 统计结果
    print(f"\n" + "="*80)
    print("URL更新完成统计")
    print("="*80)
    print(f"总游戏数: {len(df)}")
    print(f"已更新URL: {updated_count}")
    print(f"跳过更新: {skipped_count}")
    print(f"更新失败: {error_count}")
    print(f"更新率: {updated_count/(updated_count + error_count)*100:.1f}%" if (updated_count + error_count) > 0 else "N/A")
    
    # 显示更新状态统计
    if 'video_url_updated' in df.columns:
        update_status = df['video_url_updated'].value_counts()
        print(f"\nURL更新状态:")
        for status, count in update_status.items():
            if status:  # 只显示非空状态
                print(f"  {status}: {count}")
    
    return df

def analyze_updated_urls():
    """分析更新后的URL"""
    print(f"\n" + "="*60)
    print("更新后URL分析")
    print("="*60)
    
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        # 统计不同类型的video URL
        local_videos = df[df['video'].str.startswith('videos/', na=False)]
        new_format_videos = df[df['video'].str.contains('1games.io/data/file/game/', na=False)]
        old_format_videos = df[df['video'].str.contains('cdn.1games.io', na=False)]
        other_videos = df[df['video'].notna() & 
                         ~df['video'].str.startswith('videos/', na=False) & 
                         ~df['video'].str.contains('1games.io/data/file/game/', na=False) &
                         ~df['video'].str.contains('cdn.1games.io', na=False)]
        
        print(f"Video URL类型统计:")
        print(f"  本地文件 (videos/): {len(local_videos)}")
        print(f"  新格式 (1games.io/data/): {len(new_format_videos)}")
        print(f"  旧格式 (cdn.1games.io): {len(old_format_videos)}")
        print(f"  其他格式: {len(other_videos)}")
        print(f"  空/无效: {len(df) - len(local_videos) - len(new_format_videos) - len(old_format_videos) - len(other_videos)}")
        
        # 显示新格式URL示例
        if len(new_format_videos) > 0:
            print(f"\n新格式URL示例:")
            for i, (_, row) in enumerate(new_format_videos.head(5).iterrows(), 1):
                print(f"  {i}. {row['标题']}")
                print(f"     {row['video']}")
        
        return len(new_format_videos)
        
    except Exception as e:
        print(f"分析失败: {e}")
        return 0

if __name__ == "__main__":
    df = update_failed_video_urls()
    if df is not None:
        new_urls_count = analyze_updated_urls()
        print(f"\n🎯 准备下载 {new_urls_count} 个新格式的video URL")
