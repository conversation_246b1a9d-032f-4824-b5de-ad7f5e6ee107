#!/usr/bin/env python3
"""
最终验证 merged_games_list.xlsx 的处理结果
"""

import pandas as pd
import re
import json

def final_verification():
    """最终验证处理结果"""
    try:
        print("="*80)
        print("最终验证 merged_games_list.xlsx")
        print("="*80)
        
        # 读取处理后的文件
        df = pd.read_excel('merged_games_list.xlsx')
        
        print(f"✅ 文件读取成功")
        print(f"📊 基本信息:")
        print(f"   - 总行数: {len(df):,}")
        print(f"   - 总列数: {len(df.columns)}")
        
        # 1. 验证Tag列处理
        print(f"\n🏷️  Tag列验证:")
        if 'tag' in df.columns:
            # 统计格式
            comma_format = 0
            multiline_format = 0
            empty_tags = 0
            
            for tag in df['tag']:
                if pd.isna(tag) or tag == '':
                    empty_tags += 1
                elif ',' in str(tag) and '\n' not in str(tag):
                    comma_format += 1
                elif '\n' in str(tag):
                    multiline_format += 1
            
            print(f"   逗号分隔格式: {comma_format}")
            print(f"   多行格式: {multiline_format}")
            print(f"   空标签: {empty_tags}")
            
            # 显示样本
            print(f"   Tag样本:")
            count = 0
            for tag in df['tag']:
                if pd.notna(tag) and tag != '' and count < 3:
                    print(f"     {count+1}. {tag}")
                    count += 1
        
        # 2. 验证简介列处理
        print(f"\n📝 简介列验证:")
        if '简介' in df.columns:
            # JSON兼容性测试
            json_compatible = 0
            json_incompatible = 0
            has_real_newlines = 0
            has_links = 0
            has_unescaped_quotes = 0
            
            for intro in df['简介']:
                if pd.notna(intro) and intro != '':
                    intro_str = str(intro)
                    
                    # 测试JSON兼容性
                    try:
                        json.dumps({"test": intro_str})
                        json_compatible += 1
                    except:
                        json_incompatible += 1
                    
                    # 检查真实换行符
                    if '\n' in intro_str:
                        has_real_newlines += 1
                    
                    # 检查超链接
                    if re.search(r'\[.*?\]\(.*?\)', intro_str):
                        has_links += 1
                    
                    # 检查未转义的双引号
                    if '"' in intro_str and '\\"' not in intro_str:
                        has_unescaped_quotes += 1
            
            print(f"   JSON兼容: {json_compatible}")
            print(f"   JSON不兼容: {json_incompatible}")
            print(f"   包含真实换行符: {has_real_newlines}")
            print(f"   包含超链接: {has_links}")
            print(f"   包含未转义双引号: {has_unescaped_quotes}")
            
            # 显示样本
            print(f"   简介样本:")
            count = 0
            for intro in df['简介']:
                if pd.notna(intro) and intro != '' and count < 2:
                    sample = str(intro)[:100] + "..." if len(str(intro)) > 100 else str(intro)
                    print(f"     {count+1}. {sample}")
                    count += 1
        
        # 3. 数据完整性检查
        print(f"\n📊 数据完整性:")
        for col in df.columns:
            non_null = df[col].notna().sum()
            null_count = df[col].isna().sum()
            print(f"   {col:<25}: 非空 {non_null:,} | 空值 {null_count:,}")
        
        # 4. 创建JSON测试
        print(f"\n🧪 JSON格式测试:")
        
        # 选择前3条记录进行JSON测试
        test_records = []
        for i in range(min(3, len(df))):
            record = {}
            for col in df.columns:
                value = df.iloc[i][col]
                if pd.isna(value):
                    record[col] = None
                else:
                    record[col] = str(value)
            test_records.append(record)
        
        try:
            json_str = json.dumps(test_records, ensure_ascii=False, indent=2)
            print(f"   ✅ JSON序列化成功")
            print(f"   JSON长度: {len(json_str):,} 字符")
            
            # 显示JSON样本
            json_sample = json_str[:300] + "..." if len(json_str) > 300 else json_str
            print(f"   JSON样本:\n{json_sample}")
            
        except Exception as e:
            print(f"   ❌ JSON序列化失败: {e}")
        
        # 5. 最终评估
        print(f"\n🎯 最终评估:")
        
        # Tag列评估
        tag_score = 0
        if 'tag' in df.columns:
            comma_count = sum(1 for tag in df['tag'] if pd.notna(tag) and ',' in str(tag) and '\n' not in str(tag))
            total_valid_tags = sum(1 for tag in df['tag'] if pd.notna(tag) and tag != '')
            if total_valid_tags > 0:
                tag_score = comma_count / total_valid_tags * 100
        
        # 简介列评估
        intro_score = 0
        if '简介' in df.columns:
            json_count = sum(1 for intro in df['简介'] if pd.notna(intro) and intro != '' and test_json_compatibility(str(intro)))
            total_valid_intros = sum(1 for intro in df['简介'] if pd.notna(intro) and intro != '')
            if total_valid_intros > 0:
                intro_score = json_count / total_valid_intros * 100
        
        print(f"   Tag列处理质量: {tag_score:.1f}%")
        print(f"   简介列处理质量: {intro_score:.1f}%")
        print(f"   整体处理质量: {(tag_score + intro_score) / 2:.1f}%")
        
        if tag_score >= 95 and intro_score >= 95:
            print(f"   🎉 处理质量: 优秀")
        elif tag_score >= 90 and intro_score >= 90:
            print(f"   ✅ 处理质量: 良好")
        else:
            print(f"   ⚠️  处理质量: 需要改进")
        
        print(f"\n✅ 验证完成！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_compatibility(text):
    """测试JSON兼容性"""
    try:
        json.dumps({"test": text})
        return True
    except:
        return False

if __name__ == "__main__":
    final_verification()
