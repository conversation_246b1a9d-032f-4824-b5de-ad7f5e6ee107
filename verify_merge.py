#!/usr/bin/env python3
"""
验证Excel合并结果
"""

import pandas as pd
import sys

def verify_merge():
    """验证合并结果"""
    try:
        print("正在验证合并结果...")
        
        # 读取合并后的文件
        df = pd.read_excel('merged_games.xlsx')
        
        print(f"✅ 文件读取成功")
        print(f"📊 基本信息:")
        print(f"   - 总行数: {len(df):,}")
        print(f"   - 总列数: {len(df.columns)}")
        
        print(f"\n📋 列信息:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # 检查关键列
        key_columns = ['标题', '分类', '技术', '平台', '简介']
        print(f"\n🔍 关键列检查:")
        for col in key_columns:
            if col in df.columns:
                non_null = df[col].notna().sum()
                print(f"   ✅ {col}: {non_null:,} 条非空记录")
            else:
                print(f"   ❌ {col}: 列不存在")
        
        # 显示样本数据
        print(f"\n📝 样本数据 (前2条):")
        for i in range(min(2, len(df))):
            print(f"\n   第 {i+1} 条:")
            print(f"   标题: {df.iloc[i]['标题']}")
            if '分类' in df.columns:
                print(f"   分类: {df.iloc[i]['分类']}")
            if '技术' in df.columns:
                print(f"   技术: {df.iloc[i]['技术']}")
            if '平台' in df.columns:
                print(f"   平台: {df.iloc[i]['平台']}")
        
        print(f"\n✅ 验证完成！合并成功。")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = verify_merge()
    sys.exit(0 if success else 1)
