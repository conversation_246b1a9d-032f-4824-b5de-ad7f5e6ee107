#!/usr/bin/env python3
"""
最终处理 merged_games_list.xlsx
1. Tag列：多行格式转逗号分隔
2. 简介列：转换为JSON兼容的Markdown格式
3. Video列：检查文件存在性
4. 输出新文件，不覆盖原文件
"""

import pandas as pd
import re
import os
import glob
from urllib.parse import urlparse

def clean_tag_column(tag_text):
    """
    处理tag列，将多行格式转换为逗号分隔
    """
    if pd.isna(tag_text) or not tag_text:
        return ""
    
    # 转换为字符串并按行分割
    lines = str(tag_text).strip().split('\n')
    
    # 提取非数字行作为标签
    tags = []
    for line in lines:
        line = line.strip()
        # 跳过空行和纯数字行
        if line and not line.isdigit():
            tags.append(line)
    
    # 去重并用逗号连接
    unique_tags = []
    for tag in tags:
        if tag not in unique_tags:
            unique_tags.append(tag)
    
    return ','.join(unique_tags)

def html_to_markdown(html_text):
    """
    将HTML转换为Markdown格式
    """
    if pd.isna(html_text) or not html_text:
        return ""

    try:
        text = str(html_text)

        # 解码HTML实体
        import html
        text = html.unescape(text)

        # HTML到Markdown转换
        # 处理标题
        text = re.sub(r'<h([1-6])[^>]*>(.*?)</h[1-6]>', lambda m: '#' * int(m.group(1)) + ' ' + m.group(2), text, flags=re.IGNORECASE | re.DOTALL)

        # 处理粗体
        text = re.sub(r'<(b|strong)[^>]*>(.*?)</\1>', r'**\2**', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理斜体
        text = re.sub(r'<(i|em)[^>]*>(.*?)</\1>', r'*\2*', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理链接 - 移除超链接，保留文本
        text = re.sub(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>', r'\2', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理段落
        text = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\\n\\n', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理换行
        text = re.sub(r'<br[^>]*/?>', '\\n', text, flags=re.IGNORECASE)

        # 处理列表
        text = re.sub(r'<li[^>]*>(.*?)</li>', r'- \1\\n', text, flags=re.IGNORECASE | re.DOTALL)
        text = re.sub(r'<[uo]l[^>]*>(.*?)</[uo]l>', r'\1', text, flags=re.IGNORECASE | re.DOTALL)

        # 移除其他HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 处理Markdown链接格式 - 移除超链接
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)

        # 处理双引号转义
        text = text.replace('"', '\\"')

        # 清理多余的空行和空格
        text = re.sub(r'\\n\\s*\\n\\s*\\n', '\\n\\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = text.strip()

        return text

    except Exception as e:
        print(f"HTML转换错误: {e}")
        return str(html_text)

def process_intro_for_json(intro_text):
    """
    处理简介文本，转换为JSON兼容的Markdown格式
    """
    if pd.isna(intro_text) or not intro_text:
        return ""

    # 先转换为Markdown
    text = html_to_markdown(intro_text)

    return text

def extract_game_slug_from_url(url):
    """从URL中提取游戏slug"""
    if pd.isna(url) or not url:
        return None
    
    try:
        path = urlparse(url).path
        game_slug = path.strip('/').split('/')[-1]
        return game_slug if game_slug else None
    except:
        return None

def normalize_title_for_matching(title):
    """标准化标题用于文件名匹配"""
    if pd.isna(title) or not title:
        return ""
    
    # 转换为小写
    title = str(title).lower()
    # 移除特殊字符，只保留字母数字和连字符
    title = re.sub(r'[^a-z0-9\-\s]', '', title)
    # 将空格转换为连字符
    title = re.sub(r'\s+', '-', title)
    # 移除多余的连字符
    title = re.sub(r'-+', '-', title)
    # 移除首尾连字符
    title = title.strip('-')
    
    return title

def check_video_exists(title, title_url, video_files):
    """
    检查视频文件是否存在
    """
    if not video_files:
        return ""
    
    # 方法1: 从URL提取slug进行匹配
    game_slug = extract_game_slug_from_url(title_url)
    if game_slug:
        exact_match = f"{game_slug}.mp4"
        if exact_match in video_files:
            return "存在"
        
        # 部分匹配
        for video_file in video_files:
            if game_slug in video_file:
                return "存在"
    
    # 方法2: 标准化标题进行匹配
    normalized_title = normalize_title_for_matching(title)
    if normalized_title:
        exact_match = f"{normalized_title}.mp4"
        if exact_match in video_files:
            return "存在"
        
        # 部分匹配
        for video_file in video_files:
            video_name = os.path.splitext(video_file)[0]
            if normalized_title in video_name or video_name in normalized_title:
                return "存在"
    
    return ""

def get_video_files():
    """获取videos文件夹中的所有视频文件"""
    video_folder = "videos"
    if not os.path.exists(video_folder):
        return []
    
    # 获取所有视频文件
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.webm']
    video_files = []
    
    for ext in video_extensions:
        files = glob.glob(os.path.join(video_folder, ext))
        video_files.extend([os.path.basename(f) for f in files])
    
    return video_files

def process_final_games_list():
    """最终处理游戏列表文件"""
    
    print("="*80)
    print("最终处理 merged_games_list.xlsx")
    print("="*80)
    
    try:
        # 读取文件
        print("📖 读取文件...")
        df = pd.read_excel('merged_games_list.xlsx')
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        print(f"   列名: {list(df.columns)}")
        
        # 获取视频文件列表
        print(f"\n📁 扫描视频文件...")
        video_files = get_video_files()
        print(f"   找到 {len(video_files)} 个视频文件")
        if len(video_files) > 0:
            print(f"   视频文件示例: {video_files[:5]}")
        
        # 1. 处理tag列
        if 'tag' in df.columns:
            print(f"\n🏷️  处理tag列...")
            
            # 显示处理前样本
            print("处理前tag样本:")
            for i in range(min(2, len(df))):
                if pd.notna(df.iloc[i]['tag']):
                    sample = str(df.iloc[i]['tag'])[:100] + "..." if len(str(df.iloc[i]['tag'])) > 100 else str(df.iloc[i]['tag'])
                    print(f"   第{i+1}行: {sample}")
            
            # 处理tag列
            df['tag'] = df['tag'].apply(clean_tag_column)
            
            # 显示处理后样本
            print("\n处理后tag样本:")
            for i in range(min(2, len(df))):
                if pd.notna(df.iloc[i]['tag']):
                    print(f"   第{i+1}行: {df.iloc[i]['tag']}")
        
        # 2. 处理简介列
        if '简介' in df.columns:
            print(f"\n📝 处理简介列...")
            
            # 显示处理前样本
            print("处理前简介样本:")
            for i in range(min(1, len(df))):
                if pd.notna(df.iloc[i]['简介']):
                    sample = str(df.iloc[i]['简介'])[:100] + "..." if len(str(df.iloc[i]['简介'])) > 100 else str(df.iloc[i]['简介'])
                    print(f"   第{i+1}行: {sample}")
            
            # 处理简介列
            df['简介'] = df['简介'].apply(process_intro_for_json)
            
            # 显示处理后样本
            print("\n处理后简介样本:")
            for i in range(min(1, len(df))):
                if pd.notna(df.iloc[i]['简介']):
                    sample = str(df.iloc[i]['简介'])[:100] + "..." if len(str(df.iloc[i]['简介'])) > 100 else str(df.iloc[i]['简介'])
                    print(f"   第{i+1}行: {sample}")
        
        # 3. 处理video列
        print(f"\n🎬 处理video列...")
        
        # 检查必要的列
        if '标题' not in df.columns:
            print("❌ 未找到标题列")
            return
        
        title_url_col = None
        for col in ['标题链接', 'url', 'link']:
            if col in df.columns:
                title_url_col = col
                break
        
        if not title_url_col:
            print("⚠️  未找到标题链接列，将仅使用标题进行匹配")
        
        # 处理video列
        video_exists_count = 0
        for idx, row in df.iterrows():
            title = row['标题']
            title_url = row[title_url_col] if title_url_col else ""
            
            video_status = check_video_exists(title, title_url, video_files)
            df.at[idx, 'video'] = video_status
            
            if video_status == "存在":
                video_exists_count += 1
        
        print(f"   视频匹配结果: {video_exists_count} 个游戏有对应视频")
        print(f"   匹配率: {video_exists_count/len(df)*100:.1f}%")
        
        # 保存处理后的文件
        output_file = 'games_list_processed.xlsx'
        print(f"\n💾 保存到: {output_file}")
        df.to_excel(output_file, index=False)
        
        print(f"\n✅ 处理完成!")
        print(f"   输入文件: merged_games_list.xlsx")
        print(f"   输出文件: {output_file}")
        print(f"   总行数: {len(df)}")
        print(f"   总列数: {len(df.columns)}")
        
        # 显示处理统计
        print(f"\n📊 处理统计:")
        if 'tag' in df.columns:
            valid_tags = df['tag'].notna().sum() - df['tag'].eq('').sum()
            print(f"   有效标签: {valid_tags} ({valid_tags/len(df)*100:.1f}%)")
        
        if '简介' in df.columns:
            valid_intros = df['简介'].notna().sum() - df['简介'].eq('').sum()
            print(f"   有效简介: {valid_intros} ({valid_intros/len(df)*100:.1f}%)")
        
        print(f"   有视频的游戏: {video_exists_count} ({video_exists_count/len(df)*100:.1f}%)")
        
        return df
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_final_games_list()
