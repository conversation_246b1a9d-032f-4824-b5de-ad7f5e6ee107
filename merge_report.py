#!/usr/bin/env python3
"""
生成Excel合并的详细报告
"""

import pandas as pd
import os

def generate_merge_report():
    """生成详细的合并报告"""
    
    print("="*80)
    print("Excel文件合并详细报告")
    print("="*80)
    
    try:
        # 读取合并后的文件
        merged_df = pd.read_excel('merged_games.xlsx')
        
        print(f"\n📊 合并结果概览:")
        print(f"   总记录数: {len(merged_df):,}")
        print(f"   总列数: {len(merged_df.columns)}")
        
        # 列信息
        print(f"\n📋 列信息:")
        for i, col in enumerate(merged_df.columns, 1):
            non_null_count = merged_df[col].notna().sum()
            null_count = merged_df[col].isna().sum()
            print(f"   {i:2d}. {col:<25} | 非空: {non_null_count:,} | 空值: {null_count:,}")
        
        # 数据质量检查
        print(f"\n🔍 数据质量检查:")
        
        # 检查重复标题
        duplicate_titles = merged_df['标题'].duplicated().sum()
        print(f"   重复标题数: {duplicate_titles}")
        
        if duplicate_titles > 0:
            print(f"   重复标题示例:")
            duplicates = merged_df[merged_df['标题'].duplicated(keep=False)]['标题'].value_counts().head(5)
            for title, count in duplicates.items():
                print(f"     - {title}: {count}次")
        
        # 检查标签分布
        if '标签' in merged_df.columns:
            print(f"\n🏷️  标签分布 (前10个):")
            tag_counts = merged_df['标签'].value_counts().head(10)
            for tag, count in tag_counts.items():
                print(f"   {tag:<20}: {count:,}")
        
        # 检查分类分布
        if '分类' in merged_df.columns:
            print(f"\n📂 分类分布:")
            category_counts = merged_df['分类'].value_counts()
            for category, count in category_counts.items():
                print(f"   {category:<20}: {count:,}")
        
        # 检查技术分布
        if '技术' in merged_df.columns:
            print(f"\n⚙️  技术分布:")
            tech_counts = merged_df['技术'].value_counts()
            for tech, count in tech_counts.items():
                print(f"   {tech:<20}: {count:,}")
        
        # 检查平台分布
        if '平台' in merged_df.columns:
            print(f"\n💻 平台分布:")
            platform_counts = merged_df['平台'].value_counts()
            for platform, count in platform_counts.items():
                print(f"   {platform:<20}: {count:,}")
        
        # 媒体文件统计
        print(f"\n🎬 媒体文件统计:")
        if 'video' in merged_df.columns:
            has_video = merged_df['video'].notna().sum()
            print(f"   有视频的游戏: {has_video:,} ({has_video/len(merged_df)*100:.1f}%)")
        
        if '图片' in merged_df.columns:
            has_image = merged_df['图片'].notna().sum()
            print(f"   有图片的游戏: {has_image:,} ({has_image/len(merged_df)*100:.1f}%)")
        
        # 评分统计
        if 'card_rating' in merged_df.columns:
            print(f"\n⭐ 评分统计:")
            has_rating = merged_df['card_rating'].notna().sum()
            print(f"   有评分的游戏: {has_rating:,} ({has_rating/len(merged_df)*100:.1f}%)")
            
            if has_rating > 0:
                avg_rating = merged_df['card_rating'].mean()
                min_rating = merged_df['card_rating'].min()
                max_rating = merged_df['card_rating'].max()
                print(f"   平均评分: {avg_rating:.2f}")
                print(f"   评分范围: {min_rating} - {max_rating}")
        
        # 样本数据展示
        print(f"\n📝 样本数据 (前3条):")
        for i in range(min(3, len(merged_df))):
            print(f"\n   === 第 {i+1} 条记录 ===")
            for col in merged_df.columns:
                value = merged_df.iloc[i][col]
                if pd.isna(value):
                    value = "[空]"
                elif isinstance(value, str) and len(str(value)) > 50:
                    value = str(value)[:47] + "..."
                print(f"   {col}: {value}")
        
        print(f"\n" + "="*80)
        print("报告生成完成")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_merge_report()
