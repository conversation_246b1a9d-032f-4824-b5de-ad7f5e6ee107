#!/usr/bin/env python3
"""
下载新格式的video URL
"""

import pandas as pd
import os
import requests
import time
from urllib.parse import urlparse, unquote
import hashlib

def create_folders():
    """创建必要的文件夹"""
    if not os.path.exists('videos'):
        os.makedirs('videos')
        print(f"创建文件夹: videos/")

def get_filename_from_url(url, title=None):
    """从URL生成文件名"""
    try:
        parsed = urlparse(url)
        path = unquote(parsed.path)
        
        # 尝试从URL路径获取文件名
        if '/' in path:
            filename = path.split('/')[-1]
            if '.' in filename:
                return filename
        
        # 如果无法从URL获取，使用标题生成
        if title:
            # 清理标题，移除特殊字符
            clean_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_title = clean_title.replace(' ', '-').lower()
            return f"{clean_title}.mp4"
        
        # 最后使用URL的hash作为文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        return f"{url_hash}.mp4"
            
    except Exception as e:
        print(f"生成文件名失败: {e}")
        return None

def download_file(url, filepath, timeout=60):
    """下载单个文件"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout, stream=True)
        response.raise_for_status()
        
        # 检查文件大小
        content_length = response.headers.get('content-length')
        if content_length:
            file_size = int(content_length)
            if file_size > 200 * 1024 * 1024:  # 200MB限制
                return False, "文件过大"
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        return True, "下载成功"
        
    except requests.exceptions.Timeout:
        return False, "下载超时"
    except requests.exceptions.RequestException as e:
        return False, f"网络错误: {str(e)}"
    except Exception as e:
        return False, f"下载失败: {str(e)}"

def download_new_format_videos(df, test_mode=True, max_downloads=50):
    """下载新格式的视频文件"""
    print("\n" + "="*60)
    print(f"下载新格式视频文件 {'(测试模式)' if test_mode else ''}")
    print("="*60)
    
    # 筛选新格式的video URL
    new_format_videos = df[df['video'].str.contains('1games.io/data/file/game/', na=False)]
    
    if test_mode:
        new_format_videos = new_format_videos.head(max_downloads)
        print(f"测试模式: 只下载前 {max_downloads} 个视频")
    
    print(f"找到 {len(new_format_videos)} 个新格式video URL")
    
    if len(new_format_videos) == 0:
        print("没有找到新格式的video URL")
        return 0, 0, 0
    
    success_count = 0
    fail_count = 0
    skip_count = 0
    
    for i, (idx, row) in enumerate(new_format_videos.iterrows(), 1):
        video_url = row['video']
        title = row['标题']
        
        # 生成文件名
        filename = get_filename_from_url(video_url, title)
        if not filename:
            df.at[idx, 'video_download_status'] = '文件名生成失败'
            fail_count += 1
            continue
        
        filepath = os.path.join('videos', filename)
        
        # 检查文件是否已存在
        if os.path.exists(filepath):
            df.at[idx, 'video_download_status'] = '已存在'
            skip_count += 1
            print(f"跳过 {i}/{len(new_format_videos)}: {title} (文件已存在)")
            continue
        
        print(f"下载 {i}/{len(new_format_videos)}: {title}")
        print(f"  URL: {video_url}")
        print(f"  文件: {filepath}")
        
        success, message = download_file(video_url, filepath)
        
        if success:
            df.at[idx, 'video_download_status'] = '下载成功'
            success_count += 1
            
            # 显示文件大小
            file_size = os.path.getsize(filepath) / 1024 / 1024
            print(f"  ✅ {message} ({file_size:.1f} MB)")
        else:
            df.at[idx, 'video_download_status'] = f'下载失败: {message}'
            fail_count += 1
            print(f"  ❌ {message}")
        
        # 添加延迟避免过于频繁的请求
        time.sleep(1)
        
        # 每10个文件显示一次进度
        if i % 10 == 0:
            print(f"\n进度: {i}/{len(new_format_videos)} - 成功: {success_count}, 失败: {fail_count}, 跳过: {skip_count}")
    
    print(f"\n新格式视频下载完成!")
    print(f"  成功: {success_count}")
    print(f"  失败: {fail_count}")
    print(f"  跳过: {skip_count}")
    
    return success_count, fail_count, skip_count

def analyze_download_results(df):
    """分析下载结果"""
    print(f"\n" + "="*60)
    print("下载结果分析")
    print("="*60)
    
    # 统计下载状态
    if 'video_download_status' in df.columns:
        status_counts = df['video_download_status'].value_counts()
        
        print(f"视频下载状态统计:")
        for status, count in status_counts.items():
            if status:  # 只显示非空状态
                percentage = count / len(df) * 100
                print(f"  {status}: {count} ({percentage:.1f}%)")
        
        # 分析成功率
        success_keywords = ['成功', '已存在', '本地文件']
        fail_keywords = ['失败', '404', '超时', '错误']
        
        success_count = sum(count for status, count in status_counts.items() 
                          if any(keyword in str(status) for keyword in success_keywords))
        fail_count = sum(count for status, count in status_counts.items() 
                        if any(keyword in str(status) for keyword in fail_keywords))
        
        total_with_status = success_count + fail_count
        if total_with_status > 0:
            success_rate = success_count / total_with_status * 100
            print(f"\n总体成功率: {success_rate:.1f}% ({success_count}/{total_with_status})")
        
        # 分析新格式URL的成功率
        new_format_videos = df[df['video'].str.contains('1games.io/data/file/game/', na=False)]
        if len(new_format_videos) > 0:
            new_success = len(new_format_videos[new_format_videos['video_download_status'].str.contains('成功|已存在', na=False)])
            new_fail = len(new_format_videos[new_format_videos['video_download_status'].str.contains('失败|404|超时|错误', na=False)])
            
            if new_success + new_fail > 0:
                new_success_rate = new_success / (new_success + new_fail) * 100
                print(f"新格式URL成功率: {new_success_rate:.1f}% ({new_success}/{new_success + new_fail})")

def main():
    """主函数"""
    print("="*80)
    print("新格式Video下载程序")
    print("="*80)
    
    # 创建文件夹
    create_folders()
    
    # 读取Excel文件
    try:
        df = pd.read_excel('merged_games.xlsx')
        print(f"读取Excel文件成功: {len(df)} 个游戏")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 显示当前状态
    new_format_count = len(df[df['video'].str.contains('1games.io/data/file/game/', na=False)])
    local_file_count = len(df[df['video'].str.startswith('videos/', na=False)])
    
    print(f"\n当前video状态:")
    print(f"  新格式URL: {new_format_count}")
    print(f"  本地文件: {local_file_count}")
    print(f"  其他: {len(df) - new_format_count - local_file_count}")
    
    # 询问下载模式
    print(f"\n请选择下载模式:")
    print(f"1. 测试模式 (下载前10个)")
    print(f"2. 批量测试 (下载前50个)")
    print(f"3. 完整下载 (下载所有新格式URL)")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        success, fail, skip = download_new_format_videos(df, test_mode=True, max_downloads=10)
    elif choice == "2":
        success, fail, skip = download_new_format_videos(df, test_mode=True, max_downloads=50)
    elif choice == "3":
        success, fail, skip = download_new_format_videos(df, test_mode=False)
    else:
        print("无效选择")
        return
    
    # 保存更新后的Excel文件
    try:
        df.to_excel('merged_games.xlsx', index=False)
        print(f"\n✅ Excel文件已更新，包含下载状态信息")
    except Exception as e:
        print(f"❌ 保存Excel文件失败: {e}")
    
    # 分析结果
    analyze_download_results(df)
    
    # 最终统计
    print(f"\n" + "="*80)
    print("下载任务完成")
    print("="*80)
    print(f"新格式视频下载: 成功 {success}, 失败 {fail}, 跳过 {skip}")

if __name__ == "__main__":
    main()
