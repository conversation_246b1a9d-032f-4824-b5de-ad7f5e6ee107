#!/usr/bin/env python3
"""
简单检查video列的内容
"""

try:
    import pandas as pd
    
    # 读取Excel文件
    df = pd.read_excel('merged_games.xlsx')
    
    print("文件列名:", list(df.columns))
    print("文件形状:", df.shape)
    
    if 'video' in df.columns:
        print("\nVideo列统计:")
        print("非空video数量:", df['video'].notna().sum())
        print("空video数量:", df['video'].isna().sum())
        
        # 显示所有非空的video
        print("\n所有非空video内容:")
        non_empty_videos = df[df['video'].notna()]
        for i, (idx, row) in enumerate(non_empty_videos.iterrows(), 1):
            print(f"{i:2d}. 标题: {row['标题']}")
            print(f"    链接: {row['标题链接']}")
            print(f"    视频: {row['video']}")
            print()
    else:
        print("未找到video列")
        
except ImportError:
    print("pandas未安装，尝试安装...")
    import subprocess
    subprocess.run(['pip3', 'install', 'pandas', 'openpyxl'])
    
except Exception as e:
    print(f"错误: {e}")
