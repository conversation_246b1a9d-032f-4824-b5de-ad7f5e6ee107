#!/usr/bin/env python3
"""
检查最终下载状态
"""

import os
import pandas as pd

def check_final_status():
    """检查最终下载状态"""
    
    print("="*80)
    print("最终下载状态检查")
    print("="*80)
    
    # 1. 检查文件夹状态
    if os.path.exists('videos'):
        video_files = [f for f in os.listdir('videos') if f.lower().endswith('.mp4')]
        total_size = sum(os.path.getsize(os.path.join('videos', f)) for f in video_files) / 1024 / 1024
        
        print(f"\n📁 Videos文件夹状态:")
        print(f"   - 视频文件总数: {len(video_files)}")
        print(f"   - 文件夹大小: {total_size:.1f} MB")
        
        # 计算下载进度
        original_count = 159  # 原始文件数
        new_downloads = len(video_files) - original_count
        target_downloads = 785  # 目标下载数
        
        if new_downloads > 0:
            progress = new_downloads / target_downloads * 100
            print(f"   - 新下载文件: {new_downloads}/{target_downloads}")
            print(f"   - 下载进度: {progress:.1f}%")
            
            if progress >= 99:
                print(f"   🎉 下载基本完成!")
            elif progress >= 90:
                print(f"   🔥 下载接近完成!")
            else:
                remaining = target_downloads - new_downloads
                print(f"   ⏳ 剩余文件: {remaining}")
    
    # 2. 检查Excel状态
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        if 'video_download_status' in df.columns:
            print(f"\n📊 Excel下载状态:")
            
            # 统计新格式URL的下载状态
            new_format_videos = df[df['video'].str.contains('1games.io/data/file/game/', na=False)]
            status_counts = new_format_videos['video_download_status'].value_counts()
            
            success_count = sum(count for status, count in status_counts.items() if '成功' in str(status) or '已存在' in str(status))
            fail_count = sum(count for status, count in status_counts.items() if '失败' in str(status))
            pending_count = len(new_format_videos) - success_count - fail_count
            
            print(f"   - 新格式URL总数: {len(new_format_videos)}")
            print(f"   - 下载成功: {success_count}")
            print(f"   - 下载失败: {fail_count}")
            print(f"   - 待处理: {pending_count}")
            
            if pending_count == 0:
                print(f"   ✅ 所有新格式URL处理完成!")
                success_rate = success_count / len(new_format_videos) * 100
                print(f"   📈 成功率: {success_rate:.1f}%")
    
    except Exception as e:
        print(f"读取Excel失败: {e}")
    
    # 3. 整体完成情况
    print(f"\n🎯 整体完成情况:")
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        # 统计有视频的游戏
        has_video = len(df[(df['video_download_status'].str.contains('成功|本地文件|已存在', na=False)) | 
                          (df['video'].str.startswith('videos/', na=False))])
        
        # 统计有图片的游戏
        has_image = len(df[df['image_download_status'].str.contains('成功|已存在', na=False)])
        
        print(f"   - 有视频的游戏: {has_video}/{len(df)} ({has_video/len(df)*100:.1f}%)")
        print(f"   - 有图片的游戏: {has_image}/{len(df)} ({has_image/len(df)*100:.1f}%)")
        print(f"   - 完整媒体文件: {min(has_image, has_video)}/{len(df)} ({min(has_image, has_video)/len(df)*100:.1f}%)")
        
    except Exception as e:
        print(f"统计完成情况失败: {e}")

if __name__ == "__main__":
    check_final_status()
