#!/usr/bin/env python3
"""
测试下载程序 - 只下载前5个文件
"""

import pandas as pd
import os
import requests
import time
from urllib.parse import urlparse, unquote
import hashlib

def create_folders():
    """创建必要的文件夹"""
    folders = ['images', 'videos']
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"创建文件夹: {folder}/")

def get_filename_from_url(url, title=None):
    """从URL生成文件名"""
    try:
        parsed = urlparse(url)
        path = unquote(parsed.path)
        
        # 尝试从URL路径获取文件名
        if '/' in path:
            filename = path.split('/')[-1]
            if '.' in filename:
                return filename
        
        # 如果无法从URL获取，使用标题生成
        if title:
            # 清理标题，移除特殊字符
            clean_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_title = clean_title.replace(' ', '-').lower()
            
            # 从URL获取扩展名
            if '.' in path:
                ext = path.split('.')[-1].split('?')[0]  # 移除查询参数
                return f"{clean_title}.{ext}"
            else:
                return f"{clean_title}.mp4"  # 默认扩展名
        
        # 最后使用URL的hash作为文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        if '.webp' in url:
            return f"{url_hash}.webp"
        elif '.mp4' in url:
            return f"{url_hash}.mp4"
        else:
            return f"{url_hash}.file"
            
    except Exception as e:
        print(f"生成文件名失败: {e}")
        return None

def download_file(url, filepath, timeout=30):
    """下载单个文件"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout, stream=True)
        response.raise_for_status()
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        return True, "下载成功"
        
    except requests.exceptions.Timeout:
        return False, "下载超时"
    except requests.exceptions.RequestException as e:
        return False, f"网络错误: {str(e)}"
    except Exception as e:
        return False, f"下载失败: {str(e)}"

def test_download():
    """测试下载前5个文件"""
    print("="*60)
    print("测试下载程序")
    print("="*60)
    
    # 创建文件夹
    create_folders()
    
    # 读取Excel文件
    try:
        df = pd.read_excel('merged_games.xlsx')
        print(f"读取Excel文件成功: {len(df)} 个游戏")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 测试下载前5个图片
    print(f"\n测试下载前5个图片:")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        image_url = row['图片']
        title = row['标题']
        
        if pd.isna(image_url):
            print(f"{i+1}. {title}: 无图片URL")
            continue
        
        filename = get_filename_from_url(image_url, title)
        if not filename:
            print(f"{i+1}. {title}: 文件名生成失败")
            continue
        
        filepath = os.path.join('images', filename)
        
        print(f"{i+1}. {title}")
        print(f"   URL: {image_url}")
        print(f"   文件: {filepath}")
        
        success, message = download_file(image_url, filepath)
        print(f"   结果: {message}")
        
        if success:
            file_size = os.path.getsize(filepath) / 1024
            print(f"   大小: {file_size:.1f} KB")
        
        time.sleep(1)
    
    # 测试下载前3个在线视频
    print(f"\n测试下载前3个在线视频:")
    online_videos = df[df['video'].str.contains('https://', na=False)]
    
    for i in range(min(3, len(online_videos))):
        row = online_videos.iloc[i]
        video_url = row['video']
        title = row['标题']
        
        filename = get_filename_from_url(video_url, title)
        if not filename:
            print(f"{i+1}. {title}: 文件名生成失败")
            continue
        
        filepath = os.path.join('videos', filename)
        
        print(f"{i+1}. {title}")
        print(f"   URL: {video_url}")
        print(f"   文件: {filepath}")
        
        success, message = download_file(video_url, filepath, timeout=60)
        print(f"   结果: {message}")
        
        if success:
            file_size = os.path.getsize(filepath) / 1024
            print(f"   大小: {file_size:.1f} KB")
        
        time.sleep(2)
    
    print(f"\n测试完成!")

if __name__ == "__main__":
    test_download()
