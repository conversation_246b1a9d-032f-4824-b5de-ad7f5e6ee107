#!/usr/bin/env python3
"""
处理 merged_games_list.xlsx 文件
1. 处理tag列：将多行格式转换为逗号分隔
2. 处理简介列：将HTML转换为Markdown
"""

import pandas as pd
import re
import html

def clean_tag_column(tag_text):
    """
    处理tag列，将多行格式转换为逗号分隔
    输入: "Sports\n105\nSoccer\n15\n2 Player\n40\nPhysics\n100\nRagdoll\n37"
    输出: "Sports,Soccer,2 Player,Physics,Ragdoll"
    """
    if pd.isna(tag_text) or not tag_text:
        return ""
    
    # 转换为字符串并按行分割
    lines = str(tag_text).strip().split('\n')
    
    # 提取非数字行作为标签
    tags = []
    for line in lines:
        line = line.strip()
        # 跳过空行和纯数字行
        if line and not line.isdigit():
            tags.append(line)
    
    # 去重并用逗号连接
    unique_tags = []
    for tag in tags:
        if tag not in unique_tags:
            unique_tags.append(tag)
    
    return ','.join(unique_tags)

def html_to_markdown(html_text):
    """
    将HTML转换为Markdown格式（使用内置库）
    """
    if pd.isna(html_text) or not html_text:
        return ""

    try:
        text = str(html_text)

        # 解码HTML实体
        text = html.unescape(text)

        # 简单的HTML到Markdown转换
        # 处理标题
        text = re.sub(r'<h([1-6])[^>]*>(.*?)</h[1-6]>', lambda m: '#' * int(m.group(1)) + ' ' + m.group(2), text, flags=re.IGNORECASE | re.DOTALL)

        # 处理粗体
        text = re.sub(r'<(b|strong)[^>]*>(.*?)</\1>', r'**\2**', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理斜体
        text = re.sub(r'<(i|em)[^>]*>(.*?)</\1>', r'*\2*', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理链接
        text = re.sub(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>', r'[\2](\1)', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理段落
        text = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\n\n', text, flags=re.IGNORECASE | re.DOTALL)

        # 处理换行
        text = re.sub(r'<br[^>]*/?>', '\n', text, flags=re.IGNORECASE)

        # 处理列表
        text = re.sub(r'<li[^>]*>(.*?)</li>', r'- \1\n', text, flags=re.IGNORECASE | re.DOTALL)
        text = re.sub(r'<[uo]l[^>]*>(.*?)</[uo]l>', r'\1', text, flags=re.IGNORECASE | re.DOTALL)

        # 移除其他HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 清理多余的空行和空格
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = text.strip()

        return text

    except Exception as e:
        print(f"HTML转换错误: {e}")
        # 如果转换失败，返回原文本
        return str(html_text)

def process_merged_games():
    """处理合并后的游戏列表文件"""
    
    print("="*80)
    print("处理 merged_games_list.xlsx")
    print("="*80)
    
    try:
        # 读取文件
        print("📖 读取文件...")
        df = pd.read_excel('merged_games_list.xlsx')
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        
        # 检查需要处理的列
        if 'tag' not in df.columns:
            print("❌ 未找到tag列")
            return
        
        if '简介' not in df.columns:
            print("❌ 未找到简介列")
            return
        
        print(f"\n🔄 处理tag列...")
        
        # 显示处理前的样本
        print("处理前tag列样本:")
        for i in range(min(3, len(df))):
            if pd.notna(df.iloc[i]['tag']):
                sample_tag = str(df.iloc[i]['tag'])[:100] + "..." if len(str(df.iloc[i]['tag'])) > 100 else str(df.iloc[i]['tag'])
                print(f"   第{i+1}行: {sample_tag}")
        
        # 处理tag列
        df['tag_processed'] = df['tag'].apply(clean_tag_column)
        
        # 显示处理后的样本
        print("\n处理后tag列样本:")
        for i in range(min(3, len(df))):
            if pd.notna(df.iloc[i]['tag_processed']):
                print(f"   第{i+1}行: {df.iloc[i]['tag_processed']}")
        
        # 统计tag处理结果
        original_empty = df['tag'].isna().sum()
        processed_empty = df['tag_processed'].eq('').sum()
        print(f"\ntag列处理统计:")
        print(f"   原始空值: {original_empty}")
        print(f"   处理后空值: {processed_empty}")
        print(f"   成功处理: {len(df) - processed_empty}")
        
        print(f"\n🔄 处理简介列...")
        
        # 显示处理前的样本
        print("处理前简介列样本:")
        for i in range(min(2, len(df))):
            if pd.notna(df.iloc[i]['简介']):
                sample_intro = str(df.iloc[i]['简介'])[:100] + "..." if len(str(df.iloc[i]['简介'])) > 100 else str(df.iloc[i]['简介'])
                print(f"   第{i+1}行: {sample_intro}")
        
        # 处理简介列
        df['简介_md'] = df['简介'].apply(html_to_markdown)
        
        # 显示处理后的样本
        print("\n处理后简介列样本:")
        for i in range(min(2, len(df))):
            if pd.notna(df.iloc[i]['简介_md']):
                sample_intro = str(df.iloc[i]['简介_md'])[:100] + "..." if len(str(df.iloc[i]['简介_md'])) > 100 else str(df.iloc[i]['简介_md'])
                print(f"   第{i+1}行: {sample_intro}")
        
        # 统计简介处理结果
        original_empty = df['简介'].isna().sum()
        processed_empty = df['简介_md'].eq('').sum()
        print(f"\n简介列处理统计:")
        print(f"   原始空值: {original_empty}")
        print(f"   处理后空值: {processed_empty}")
        print(f"   成功处理: {len(df) - processed_empty}")
        
        # 替换原列
        print(f"\n🔄 更新原列...")
        df['tag'] = df['tag_processed']
        df['简介'] = df['简介_md']
        
        # 删除临时列
        df = df.drop(['tag_processed', '简介_md'], axis=1)
        
        # 保存处理后的文件
        output_file = 'merged_games_list.xlsx'
        print(f"\n💾 保存到: {output_file}")
        df.to_excel(output_file, index=False)
        
        print(f"\n✅ 处理完成!")
        print(f"   文件: {output_file}")
        print(f"   总行数: {len(df)}")
        print(f"   总列数: {len(df.columns)}")
        
        # 显示最终样本
        print(f"\n📝 最终样本数据:")
        for i in range(min(2, len(df))):
            print(f"\n   === 第 {i+1} 条记录 ===")
            print(f"   标题: {df.iloc[i]['标题']}")
            if pd.notna(df.iloc[i]['tag']):
                print(f"   标签: {df.iloc[i]['tag']}")
            if pd.notna(df.iloc[i]['简介']):
                intro_sample = str(df.iloc[i]['简介'])[:150] + "..." if len(str(df.iloc[i]['简介'])) > 150 else str(df.iloc[i]['简介'])
                print(f"   简介: {intro_sample}")
        
        return df
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_merged_games()
