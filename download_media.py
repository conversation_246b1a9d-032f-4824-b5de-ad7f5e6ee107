#!/usr/bin/env python3
"""
下载merged_games.xlsx中的图片和视频文件
"""

import pandas as pd
import os
import requests
import time
from urllib.parse import urlparse, unquote
import hashlib

def create_folders():
    """创建必要的文件夹"""
    folders = ['images', 'videos']
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"创建文件夹: {folder}/")

def get_filename_from_url(url, title=None):
    """从URL生成文件名"""
    try:
        parsed = urlparse(url)
        path = unquote(parsed.path)
        
        # 尝试从URL路径获取文件名
        if '/' in path:
            filename = path.split('/')[-1]
            if '.' in filename:
                return filename
        
        # 如果无法从URL获取，使用标题生成
        if title:
            # 清理标题，移除特殊字符
            clean_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_title = clean_title.replace(' ', '-').lower()
            
            # 从URL获取扩展名
            if '.' in path:
                ext = path.split('.')[-1].split('?')[0]  # 移除查询参数
                return f"{clean_title}.{ext}"
            else:
                return f"{clean_title}.mp4"  # 默认扩展名
        
        # 最后使用URL的hash作为文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        if '.webp' in url:
            return f"{url_hash}.webp"
        elif '.mp4' in url:
            return f"{url_hash}.mp4"
        else:
            return f"{url_hash}.file"
            
    except Exception as e:
        print(f"生成文件名失败: {e}")
        return None

def download_file(url, filepath, timeout=30):
    """下载单个文件"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout, stream=True)
        response.raise_for_status()
        
        # 检查文件大小
        content_length = response.headers.get('content-length')
        if content_length:
            file_size = int(content_length)
            if file_size > 100 * 1024 * 1024:  # 100MB限制
                return False, "文件过大"
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        return True, "下载成功"
        
    except requests.exceptions.Timeout:
        return False, "下载超时"
    except requests.exceptions.RequestException as e:
        return False, f"网络错误: {str(e)}"
    except Exception as e:
        return False, f"下载失败: {str(e)}"

def download_images(df):
    """下载图片文件"""
    print("\n" + "="*60)
    print("开始下载图片文件")
    print("="*60)
    
    # 添加下载状态列
    if 'image_download_status' not in df.columns:
        df['image_download_status'] = ''
    
    success_count = 0
    fail_count = 0
    skip_count = 0
    
    for idx, row in df.iterrows():
        image_url = row['图片']
        title = row['标题']
        
        if pd.isna(image_url) or not image_url:
            df.at[idx, 'image_download_status'] = '无URL'
            skip_count += 1
            continue
        
        # 生成文件名
        filename = get_filename_from_url(image_url, title)
        if not filename:
            df.at[idx, 'image_download_status'] = '文件名生成失败'
            fail_count += 1
            continue
        
        filepath = os.path.join('images', filename)
        
        # 检查文件是否已存在
        if os.path.exists(filepath):
            df.at[idx, 'image_download_status'] = '已存在'
            skip_count += 1
            continue
        
        print(f"下载图片 {idx+1}/{len(df)}: {title}")
        print(f"  URL: {image_url}")
        print(f"  文件: {filepath}")
        
        success, message = download_file(image_url, filepath)
        
        if success:
            df.at[idx, 'image_download_status'] = '下载成功'
            success_count += 1
            print(f"  ✅ {message}")
        else:
            df.at[idx, 'image_download_status'] = f'下载失败: {message}'
            fail_count += 1
            print(f"  ❌ {message}")
        
        # 添加延迟避免过于频繁的请求
        time.sleep(0.5)
        
        # 每50个文件显示一次进度
        if (idx + 1) % 50 == 0:
            print(f"\n进度: {idx+1}/{len(df)} - 成功: {success_count}, 失败: {fail_count}, 跳过: {skip_count}")
    
    print(f"\n图片下载完成!")
    print(f"  成功: {success_count}")
    print(f"  失败: {fail_count}")
    print(f"  跳过: {skip_count}")
    
    return success_count, fail_count, skip_count

def download_videos(df):
    """下载视频文件"""
    print("\n" + "="*60)
    print("开始下载视频文件")
    print("="*60)
    
    # 添加下载状态列
    if 'video_download_status' not in df.columns:
        df['video_download_status'] = ''
    
    success_count = 0
    fail_count = 0
    skip_count = 0
    
    for idx, row in df.iterrows():
        video_url = row['video']
        title = row['标题']
        
        if pd.isna(video_url) or not video_url:
            df.at[idx, 'video_download_status'] = '无URL'
            skip_count += 1
            continue
        
        # 跳过本地文件
        if video_url.startswith('video/'):
            df.at[idx, 'video_download_status'] = '本地文件'
            skip_count += 1
            continue
        
        # 生成文件名
        filename = get_filename_from_url(video_url, title)
        if not filename:
            df.at[idx, 'video_download_status'] = '文件名生成失败'
            fail_count += 1
            continue
        
        filepath = os.path.join('videos', filename)
        
        # 检查文件是否已存在
        if os.path.exists(filepath):
            df.at[idx, 'video_download_status'] = '已存在'
            skip_count += 1
            continue
        
        print(f"下载视频 {idx+1}/{len(df)}: {title}")
        print(f"  URL: {video_url}")
        print(f"  文件: {filepath}")
        
        success, message = download_file(video_url, filepath, timeout=60)
        
        if success:
            df.at[idx, 'video_download_status'] = '下载成功'
            success_count += 1
            print(f"  ✅ {message}")
        else:
            df.at[idx, 'video_download_status'] = f'下载失败: {message}'
            fail_count += 1
            print(f"  ❌ {message}")
        
        # 添加延迟避免过于频繁的请求
        time.sleep(1)
        
        # 每20个文件显示一次进度
        if (idx + 1) % 20 == 0:
            print(f"\n进度: {idx+1}/{len(df)} - 成功: {success_count}, 失败: {fail_count}, 跳过: {skip_count}")
    
    print(f"\n视频下载完成!")
    print(f"  成功: {success_count}")
    print(f"  失败: {fail_count}")
    print(f"  跳过: {skip_count}")
    
    return success_count, fail_count, skip_count

def main():
    """主函数"""
    print("="*80)
    print("媒体文件下载程序")
    print("="*80)

    # 创建文件夹
    create_folders()

    # 读取Excel文件
    try:
        df = pd.read_excel('merged_games.xlsx')
        print(f"\n读取Excel文件成功: {len(df)} 个游戏")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return

    # 询问用户要下载什么
    print(f"\n请选择下载内容:")
    print(f"1. 只下载图片")
    print(f"2. 只下载视频")
    print(f"3. 下载图片和视频")
    print(f"4. 批量测试 (前50个)")

    choice = input("请输入选择 (1-4): ").strip()

    img_success = img_fail = img_skip = 0
    vid_success = vid_fail = vid_skip = 0

    if choice in ['1', '3', '4']:
        # 下载图片
        if choice == '4':
            # 只处理前50个
            df_subset = df.head(50).copy()
            img_success, img_fail, img_skip = download_images(df_subset)
            # 更新原始DataFrame
            df.loc[:49, 'image_download_status'] = df_subset['image_download_status']
        else:
            img_success, img_fail, img_skip = download_images(df)

    if choice in ['2', '3', '4']:
        # 下载视频
        if choice == '4':
            # 只处理前50个
            df_subset = df.head(50).copy()
            vid_success, vid_fail, vid_skip = download_videos(df_subset)
            # 更新原始DataFrame
            df.loc[:49, 'video_download_status'] = df_subset['video_download_status']
        else:
            vid_success, vid_fail, vid_skip = download_videos(df)

    # 保存更新后的Excel文件
    try:
        df.to_excel('merged_games.xlsx', index=False)
        print(f"\n✅ Excel文件已更新，包含下载状态信息")
    except Exception as e:
        print(f"❌ 保存Excel文件失败: {e}")

    # 最终统计
    print(f"\n" + "="*80)
    print("下载任务完成")
    print("="*80)
    print(f"图片下载: 成功 {img_success}, 失败 {img_fail}, 跳过 {img_skip}")
    print(f"视频下载: 成功 {vid_success}, 失败 {vid_fail}, 跳过 {vid_skip}")
    print(f"总计: 成功 {img_success + vid_success}, 失败 {img_fail + vid_fail}")

if __name__ == "__main__":
    main()
