#!/usr/bin/env python3
"""
验证处理后的 merged_games_list.xlsx
"""

import pandas as pd
import re

def verify_processed_games():
    """验证处理结果"""
    try:
        print("="*80)
        print("验证处理后的 merged_games_list.xlsx")
        print("="*80)
        
        # 读取处理后的文件
        df = pd.read_excel('merged_games_list.xlsx')
        
        print(f"✅ 文件读取成功")
        print(f"📊 基本信息:")
        print(f"   - 总行数: {len(df):,}")
        print(f"   - 总列数: {len(df.columns)}")
        
        # 检查tag列处理结果
        if 'tag' in df.columns:
            print(f"\n🏷️  Tag列处理验证:")
            
            # 统计tag格式
            comma_separated = 0
            multi_line = 0
            empty_tags = 0
            
            for idx, tag in enumerate(df['tag']):
                if pd.isna(tag) or tag == '':
                    empty_tags += 1
                elif ',' in str(tag) and '\n' not in str(tag):
                    comma_separated += 1
                elif '\n' in str(tag):
                    multi_line += 1
            
            print(f"   逗号分隔格式: {comma_separated}")
            print(f"   多行格式: {multi_line}")
            print(f"   空标签: {empty_tags}")
            print(f"   处理成功率: {comma_separated/(len(df)-empty_tags)*100:.1f}%")
            
            # 显示tag样本
            print(f"\n   Tag样本 (前5个):")
            count = 0
            for idx, tag in enumerate(df['tag']):
                if pd.notna(tag) and tag != '' and count < 5:
                    print(f"     {count+1}. {tag}")
                    count += 1
        
        # 检查简介列处理结果
        if '简介' in df.columns:
            print(f"\n📝 简介列处理验证:")
            
            # 统计简介格式
            markdown_format = 0
            html_format = 0
            empty_intro = 0
            
            for idx, intro in enumerate(df['简介']):
                if pd.isna(intro) or intro == '':
                    empty_intro += 1
                elif '<' in str(intro) and '>' in str(intro):
                    html_format += 1
                else:
                    markdown_format += 1
            
            print(f"   Markdown格式: {markdown_format}")
            print(f"   HTML格式: {html_format}")
            print(f"   空简介: {empty_intro}")
            print(f"   处理成功率: {markdown_format/(len(df)-empty_intro)*100:.1f}%")
            
            # 显示简介样本
            print(f"\n   简介样本 (前2个):")
            count = 0
            for idx, intro in enumerate(df['简介']):
                if pd.notna(intro) and intro != '' and count < 2:
                    sample = str(intro)[:200] + "..." if len(str(intro)) > 200 else str(intro)
                    print(f"     {count+1}. {sample}")
                    count += 1
        
        # 检查Markdown特征
        print(f"\n🔍 Markdown特征检查:")
        markdown_features = {
            '标题 (##)': 0,
            '粗体 (**)': 0,
            '斜体 (*)': 0,
            '链接 ([])': 0,
            '列表 (-)': 0
        }
        
        for intro in df['简介']:
            if pd.notna(intro):
                intro_str = str(intro)
                if '##' in intro_str:
                    markdown_features['标题 (##)'] += 1
                if '**' in intro_str:
                    markdown_features['粗体 (**)'] += 1
                if re.search(r'\*[^*]+\*', intro_str):
                    markdown_features['斜体 (*)'] += 1
                if '[' in intro_str and '](' in intro_str:
                    markdown_features['链接 ([])'] += 1
                if re.search(r'^- ', intro_str, re.MULTILINE):
                    markdown_features['列表 (-)'] += 1
        
        for feature, count in markdown_features.items():
            print(f"   {feature}: {count} 个简介包含")
        
        # 数据质量检查
        print(f"\n📊 数据质量总结:")
        print(f"   总游戏数: {len(df):,}")
        
        if 'tag' in df.columns:
            valid_tags = df['tag'].notna().sum() - df['tag'].eq('').sum()
            print(f"   有效标签: {valid_tags:,} ({valid_tags/len(df)*100:.1f}%)")
        
        if '简介' in df.columns:
            valid_intros = df['简介'].notna().sum() - df['简介'].eq('').sum()
            print(f"   有效简介: {valid_intros:,} ({valid_intros/len(df)*100:.1f}%)")
        
        print(f"\n✅ 验证完成！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_processed_games()
