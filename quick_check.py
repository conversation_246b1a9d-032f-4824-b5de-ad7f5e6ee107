#!/usr/bin/env python3
import pandas as pd

try:
    df = pd.read_excel('merged_games.xlsx')
    print(f"文件读取成功")
    print(f"行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    print(f"列名: {list(df.columns)}")
    
    # 检查前几行
    print("\n前3行数据:")
    for i in range(min(3, len(df))):
        print(f"第{i+1}行 - 标题: {df.iloc[i]['标题']}")
        if '分类' in df.columns:
            print(f"       分类: {df.iloc[i]['分类']}")
        if '技术' in df.columns:
            print(f"       技术: {df.iloc[i]['技术']}")
        print()
        
except Exception as e:
    print(f"错误: {e}")
