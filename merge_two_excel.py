#!/usr/bin/env python3
"""
合并两个Excel文件：merged_games.xlsx 和 Soccer Physics(3).xlsx
使用标题作为关联字段
"""

import pandas as pd
import os

def merge_excel_files():
    """合并两个Excel文件"""
    
    print("="*80)
    print("Excel文件合并程序")
    print("="*80)
    
    # 检查文件是否存在
    file1 = 'merged_games.xlsx'
    file2 = 'Soccer Physics(3).xlsx'
    
    if not os.path.exists(file1):
        print(f"❌ 文件不存在: {file1}")
        return
    
    if not os.path.exists(file2):
        print(f"❌ 文件不存在: {file2}")
        return
    
    try:
        # 读取第一个文件
        print(f"📖 读取文件: {file1}")
        df1 = pd.read_excel(file1)
        print(f"   行数: {len(df1)}")
        print(f"   列数: {len(df1.columns)}")
        print(f"   列名: {list(df1.columns)}")
        
        # 读取第二个文件
        print(f"\n📖 读取文件: {file2}")
        df2 = pd.read_excel(file2)
        print(f"   行数: {len(df2)}")
        print(f"   列数: {len(df2.columns)}")
        print(f"   列名: {list(df2.columns)}")
        
        # 检查是否都有标题列
        title_col1 = None
        title_col2 = None
        
        # 查找标题列
        for col in df1.columns:
            if '标题' in str(col):
                title_col1 = col
                break
        
        for col in df2.columns:
            if '标题' in str(col):
                title_col2 = col
                break
        
        if not title_col1:
            print(f"❌ 在 {file1} 中未找到标题列")
            return
        
        if not title_col2:
            print(f"❌ 在 {file2} 中未找到标题列")
            return
        
        print(f"\n🔗 使用关联字段:")
        print(f"   {file1}: {title_col1}")
        print(f"   {file2}: {title_col2}")
        
        # 显示一些样本数据
        print(f"\n📋 {file1} 前3行标题:")
        for i in range(min(3, len(df1))):
            print(f"   {i+1}. {df1.iloc[i][title_col1]}")
        
        print(f"\n📋 {file2} 前3行标题:")
        for i in range(min(3, len(df2))):
            print(f"   {i+1}. {df2.iloc[i][title_col2]}")
        
        # 合并数据
        print(f"\n🔄 开始合并...")
        
        # 方法1: 使用pandas merge进行左连接，保留df1的所有数据，添加df2的新列
        merged_df = pd.merge(df1, df2, left_on=title_col1, right_on=title_col2, how='left', suffixes=('', '_from_file2'))
        
        print(f"   合并后行数: {len(merged_df)}")
        print(f"   合并后列数: {len(merged_df.columns)}")
        
        # 检查匹配情况
        matched_count = merged_df[title_col2].notna().sum()
        print(f"   匹配的记录数: {matched_count}")
        print(f"   匹配率: {matched_count/len(df1)*100:.1f}%")
        
        # 如果有未匹配的df2数据，追加到结果中
        unmatched_df2 = df2[~df2[title_col2].isin(df1[title_col1])].copy()
        if len(unmatched_df2) > 0:
            print(f"   {file2} 中有 {len(unmatched_df2)} 条新记录将被追加")

            # 为unmatched_df2添加缺失的列
            for col in merged_df.columns:
                if col not in unmatched_df2.columns:
                    unmatched_df2.loc[:, col] = None

            # 重新排列列顺序
            unmatched_df2 = unmatched_df2[merged_df.columns]

            # 追加新记录
            merged_df = pd.concat([merged_df, unmatched_df2], ignore_index=True)
            print(f"   追加后总行数: {len(merged_df)}")
        
        # 保存结果
        output_file = 'merged_games.xlsx'
        print(f"\n💾 保存到: {output_file}")
        merged_df.to_excel(output_file, index=False)
        
        print(f"\n✅ 合并完成!")
        print(f"   最终文件: {output_file}")
        print(f"   总行数: {len(merged_df)}")
        print(f"   总列数: {len(merged_df.columns)}")
        
        # 显示列名
        print(f"\n📊 最终列名:")
        for i, col in enumerate(merged_df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        return merged_df
        
    except Exception as e:
        print(f"❌ 合并过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    merge_excel_files()
