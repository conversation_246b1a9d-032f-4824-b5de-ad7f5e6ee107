#!/usr/bin/env python3
"""
监控下载进度
"""

import os
import time
import pandas as pd

def monitor_download():
    """监控下载进度"""
    
    print("="*60)
    print("下载进度监控")
    print("="*60)
    
    while True:
        try:
            # 统计文件数量
            images_count = len([f for f in os.listdir('images') if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif'))])
            videos_count = len([f for f in os.listdir('videos') if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))])
            
            # 计算文件夹大小
            images_size = sum(os.path.getsize(os.path.join('images', f)) for f in os.listdir('images') if os.path.isfile(os.path.join('images', f))) / 1024 / 1024
            videos_size = sum(os.path.getsize(os.path.join('videos', f)) for f in os.listdir('videos') if os.path.isfile(os.path.join('videos', f))) / 1024 / 1024
            
            print(f"\r📊 当前进度: 图片 {images_count} 个 ({images_size:.1f}MB) | 视频 {videos_count} 个 ({videos_size:.1f}MB)", end="", flush=True)
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print(f"\n\n监控结束")
            break
        except Exception as e:
            print(f"\n监控出错: {e}")
            break

def check_download_status():
    """检查Excel中的下载状态"""
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        print(f"\n📋 Excel下载状态统计:")
        
        if 'image_download_status' in df.columns:
            image_status = df['image_download_status'].value_counts()
            print(f"\n图片下载状态:")
            for status, count in image_status.items():
                print(f"  {status}: {count}")
        
        if 'video_download_status' in df.columns:
            video_status = df['video_download_status'].value_counts()
            print(f"\n视频下载状态:")
            for status, count in video_status.items():
                print(f"  {status}: {count}")
                
    except Exception as e:
        print(f"检查状态失败: {e}")

if __name__ == "__main__":
    print("选择操作:")
    print("1. 实时监控下载进度")
    print("2. 检查Excel下载状态")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        monitor_download()
    elif choice == "2":
        check_download_status()
    else:
        print("无效选择")
