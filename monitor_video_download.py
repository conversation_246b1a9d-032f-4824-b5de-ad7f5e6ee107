#!/usr/bin/env python3
"""
监控视频下载进度
"""

import os
import time
import pandas as pd

def monitor_video_download():
    """实时监控视频下载进度"""
    
    print("="*60)
    print("视频下载进度监控")
    print("="*60)
    
    start_time = time.time()
    last_count = 0
    
    while True:
        try:
            # 统计videos文件夹中的文件数量
            if os.path.exists('videos'):
                video_files = [f for f in os.listdir('videos') if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
                current_count = len(video_files)
                
                # 计算文件夹大小
                total_size = sum(os.path.getsize(os.path.join('videos', f)) for f in video_files) / 1024 / 1024
                
                # 计算下载速度
                elapsed_time = time.time() - start_time
                if elapsed_time > 0:
                    download_speed = (current_count - last_count) / (elapsed_time / 60) if elapsed_time > 60 else 0
                    
                    # 估算剩余时间
                    remaining_files = 785 - current_count + 50  # 785个新格式 - 已下载 + 已跳过的50个
                    if download_speed > 0:
                        eta_minutes = remaining_files / download_speed
                        eta_hours = eta_minutes / 60
                    else:
                        eta_minutes = 0
                        eta_hours = 0
                else:
                    download_speed = 0
                    eta_minutes = 0
                    eta_hours = 0
                
                # 显示进度
                progress = (current_count - 159) / (785 - 50) * 100 if current_count > 159 else 0  # 159是原始文件数，50是已下载的
                
                print(f"\r📊 进度: {current_count} 个视频文件 ({total_size:.1f}MB) | "
                      f"进度: {progress:.1f}% | "
                      f"速度: {download_speed:.1f} 文件/分钟 | "
                      f"预计剩余: {eta_hours:.1f}小时", end="", flush=True)
                
                # 每分钟更新一次基准
                if elapsed_time > 60:
                    last_count = current_count
                    start_time = time.time()
            
            time.sleep(10)  # 每10秒检查一次
            
        except KeyboardInterrupt:
            print(f"\n\n监控结束")
            break
        except Exception as e:
            print(f"\n监控出错: {e}")
            time.sleep(5)

def check_download_progress():
    """检查下载进度详情"""
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        print(f"\n📋 下载进度详情:")
        
        # 统计新格式URL的下载状态
        new_format_videos = df[df['video'].str.contains('1games.io/data/file/game/', na=False)]
        
        if 'video_download_status' in df.columns:
            status_counts = new_format_videos['video_download_status'].value_counts()
            
            print(f"\n新格式URL下载状态 (总计 {len(new_format_videos)} 个):")
            for status, count in status_counts.items():
                if status:
                    percentage = count / len(new_format_videos) * 100
                    print(f"  {status}: {count} ({percentage:.1f}%)")
            
            # 计算完成率
            success_count = sum(count for status, count in status_counts.items() if '成功' in str(status) or '已存在' in str(status))
            completion_rate = success_count / len(new_format_videos) * 100
            print(f"\n完成率: {completion_rate:.1f}% ({success_count}/{len(new_format_videos)})")
        
        # 统计文件夹实际文件数
        if os.path.exists('videos'):
            actual_files = len([f for f in os.listdir('videos') if f.lower().endswith('.mp4')])
            print(f"实际文件数: {actual_files}")
            
    except Exception as e:
        print(f"检查进度失败: {e}")

if __name__ == "__main__":
    print("选择操作:")
    print("1. 实时监控下载进度")
    print("2. 检查当前下载状态")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        monitor_video_download()
    elif choice == "2":
        check_download_progress()
    else:
        print("无效选择")
