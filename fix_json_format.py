#!/usr/bin/env python3
"""
修复简介列的JSON格式问题
"""

import pandas as pd
import re
import json

def fix_intro_for_json(intro_text):
    """
    修复简介文本，确保JSON兼容
    """
    if pd.isna(intro_text) or not intro_text:
        return ""
    
    text = str(intro_text)
    
    # 1. 移除或转义所有可能的问题字符
    
    # 移除超链接，保留文本
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
    
    # 处理双引号
    text = text.replace('"', '\\"')
    
    # 处理反斜杠
    text = text.replace('\\', '\\\\')
    
    # 处理换行符 - 替换为 \\n
    text = text.replace('\n', '\\n')
    text = text.replace('\r', '\\n')
    
    # 处理制表符
    text = text.replace('\t', ' ')
    
    # 清理多余的空格
    text = re.sub(r'  +', ' ', text)
    
    # 清理开头和结尾的空白
    text = text.strip()
    
    return text

def test_json_compatibility(text):
    """
    测试文本是否JSON兼容
    """
    try:
        test_json = {"test": text}
        json.dumps(test_json)
        return True
    except:
        return False

def fix_merged_games_json():
    """修复合并后的游戏列表文件的JSON兼容性"""
    
    print("="*80)
    print("修复简介列的JSON兼容性")
    print("="*80)
    
    try:
        # 读取文件
        print("📖 读取文件...")
        df = pd.read_excel('merged_games_list.xlsx')
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        
        # 检查简介列
        if '简介' not in df.columns:
            print("❌ 未找到简介列")
            return
        
        print(f"\n🔍 检查当前JSON兼容性...")
        
        # 测试当前的JSON兼容性
        json_compatible = 0
        json_incompatible = 0
        
        for idx, intro in enumerate(df['简介']):
            if pd.notna(intro) and intro != '':
                if test_json_compatibility(str(intro)):
                    json_compatible += 1
                else:
                    json_incompatible += 1
        
        print(f"   JSON兼容: {json_compatible}")
        print(f"   JSON不兼容: {json_incompatible}")
        
        if json_incompatible > 0:
            print(f"\n🔄 修复JSON兼容性问题...")
            
            # 显示修复前的样本
            print("修复前样本:")
            count = 0
            for idx, intro in enumerate(df['简介']):
                if pd.notna(intro) and intro != '' and not test_json_compatibility(str(intro)) and count < 2:
                    sample = str(intro)[:100] + "..." if len(str(intro)) > 100 else str(intro)
                    print(f"   第{idx+1}行: {sample}")
                    count += 1
            
            # 修复简介列
            df['简介_fixed'] = df['简介'].apply(fix_intro_for_json)
            
            # 显示修复后的样本
            print("\n修复后样本:")
            count = 0
            for idx, intro in enumerate(df['简介_fixed']):
                if pd.notna(intro) and intro != '' and count < 2:
                    sample = str(intro)[:100] + "..." if len(str(intro)) > 100 else str(intro)
                    print(f"   第{idx+1}行: {sample}")
                    count += 1
            
            # 测试修复后的JSON兼容性
            fixed_compatible = 0
            fixed_incompatible = 0
            
            for idx, intro in enumerate(df['简介_fixed']):
                if pd.notna(intro) and intro != '':
                    if test_json_compatibility(str(intro)):
                        fixed_compatible += 1
                    else:
                        fixed_incompatible += 1
            
            print(f"\n📊 修复结果:")
            print(f"   修复前JSON兼容: {json_compatible}")
            print(f"   修复后JSON兼容: {fixed_compatible}")
            print(f"   修复成功率: {(fixed_compatible - json_compatible)/max(json_incompatible, 1)*100:.1f}%")
            
            # 替换原列
            df['简介'] = df['简介_fixed']
            df = df.drop(['简介_fixed'], axis=1)
            
        else:
            print("✅ 所有简介都已JSON兼容")
        
        # 最终验证
        print(f"\n🔍 最终验证...")
        
        final_compatible = 0
        final_incompatible = 0
        problem_indices = []
        
        for idx, intro in enumerate(df['简介']):
            if pd.notna(intro) and intro != '':
                if test_json_compatibility(str(intro)):
                    final_compatible += 1
                else:
                    final_incompatible += 1
                    problem_indices.append(idx)
        
        print(f"   最终JSON兼容: {final_compatible}")
        print(f"   最终JSON不兼容: {final_incompatible}")
        
        if final_incompatible > 0:
            print(f"   问题记录索引: {problem_indices[:5]}...")  # 只显示前5个
        
        # 保存处理后的文件
        output_file = 'merged_games_list.xlsx'
        print(f"\n💾 保存到: {output_file}")
        df.to_excel(output_file, index=False)
        
        print(f"\n✅ 修复完成!")
        print(f"   文件: {output_file}")
        print(f"   总行数: {len(df)}")
        print(f"   总列数: {len(df.columns)}")
        
        # 显示最终样本
        print(f"\n📝 最终样本数据:")
        for i in range(min(2, len(df))):
            print(f"\n   === 第 {i+1} 条记录 ===")
            print(f"   标题: {df.iloc[i]['标题']}")
            if pd.notna(df.iloc[i]['简介']):
                intro_sample = str(df.iloc[i]['简介'])[:150] + "..." if len(str(df.iloc[i]['简介'])) > 150 else str(df.iloc[i]['简介'])
                print(f"   简介: {intro_sample}")
                print(f"   JSON兼容: {test_json_compatibility(str(df.iloc[i]['简介']))}")
        
        return df
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_merged_games_json()
