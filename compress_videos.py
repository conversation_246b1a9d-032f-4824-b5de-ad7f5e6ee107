#!/usr/bin/env python3
"""
压缩videos文件夹下的mp4文件，将文件大小降低一半
"""

import os
import subprocess
import glob
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ffmpeg 可用")
            return True
        else:
            print("❌ ffmpeg 不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ ffmpeg 未安装或不在PATH中")
        return False

def create_output_folder():
    """创建输出文件夹"""
    output_folder = "videos_compressed"
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}/")
    else:
        print(f"输出文件夹已存在: {output_folder}/")
    return output_folder

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', 
            '-show_format', '-show_streams', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            import json
            info = json.loads(result.stdout)
            
            # 获取视频流信息
            video_stream = None
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if video_stream:
                width = int(video_stream.get('width', 0))
                height = int(video_stream.get('height', 0))
                duration = float(info.get('format', {}).get('duration', 0))
                bitrate = int(info.get('format', {}).get('bit_rate', 0))
                
                return {
                    'width': width,
                    'height': height,
                    'duration': duration,
                    'bitrate': bitrate
                }
        return None
    except Exception as e:
        print(f"获取视频信息失败 {video_path}: {e}")
        return None

def compress_video(input_path, output_path, speed_factor=2):
    """将视频加速到指定倍速"""
    try:
        # 获取文件大小
        file_size = os.path.getsize(input_path)

        # 如果文件太小（小于50KB），可能不是有效视频
        if file_size < 50000:
            return False, f"文件太小 ({file_size} bytes)，可能不是有效视频"

        # 获取原始视频信息
        video_info = get_video_info(input_path)

        # 构建ffmpeg命令 - 使用setpts加速视频，使用atempo加速音频
        cmd = [
            'ffmpeg', '-i', input_path,
            '-filter_complex', f'[0:v]setpts=PTS/{speed_factor}[v];[0:a]atempo={speed_factor}[a]',
            '-map', '[v]', '-map', '[a]',
            '-c:v', 'libx264',           # 使用H.264编码
            '-preset', 'fast',           # 快速编码
            '-crf', '23',                # 质量参数
            '-c:a', 'aac',               # 音频编码
            '-b:a', '128k',              # 音频比特率
            '-movflags', '+faststart',   # 优化网络播放
            '-y',                        # 覆盖输出文件
            output_path
        ]

        # 执行加速
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            return True, f"{speed_factor}倍速处理成功"
        else:
            # 尝试更简单的命令 (只加速视频，不处理音频)
            simple_cmd = [
                'ffmpeg', '-i', input_path,
                '-filter:v', f'setpts=PTS/{speed_factor}',
                '-c:v', 'libx264',
                '-an',                   # 移除音频
                '-y',
                output_path
            ]

            result2 = subprocess.run(simple_cmd, capture_output=True, text=True, timeout=300)
            if result2.returncode == 0:
                return True, f"{speed_factor}倍速处理成功(简化模式，无音频)"
            else:
                error_msg = result.stderr.split('\n')[-3:-1] if result.stderr else ["未知错误"]
                return False, f"ffmpeg错误: {' '.join(error_msg)}"

    except subprocess.TimeoutExpired:
        return False, "处理超时"
    except Exception as e:
        return False, f"处理失败: {str(e)}"

def compress_video_worker(args):
    """工作线程函数"""
    input_path, output_folder, index, total, speed_factor = args
    
    filename = os.path.basename(input_path)
    output_path = os.path.join(output_folder, filename)
    
    # 检查输出文件是否已存在
    if os.path.exists(output_path):
        original_size = os.path.getsize(input_path)
        compressed_size = os.path.getsize(output_path)
        compression_ratio = (1 - compressed_size / original_size) * 100
        return {
            'index': index,
            'filename': filename,
            'status': 'skipped',
            'message': '文件已存在',
            'original_size': original_size,
            'compressed_size': compressed_size,
            'compression_ratio': compression_ratio
        }
    
    print(f"处理 {index}/{total}: {filename}")
    
    # 获取原始文件大小
    original_size = os.path.getsize(input_path)
    
    # 加速视频
    success, message = compress_video(input_path, output_path, speed_factor)
    
    if success and os.path.exists(output_path):
        compressed_size = os.path.getsize(output_path)
        compression_ratio = (1 - compressed_size / original_size) * 100
        
        return {
            'index': index,
            'filename': filename,
            'status': 'success',
            'message': message,
            'original_size': original_size,
            'compressed_size': compressed_size,
            'compression_ratio': compression_ratio
        }
    else:
        return {
            'index': index,
            'filename': filename,
            'status': 'failed',
            'message': message,
            'original_size': original_size,
            'compressed_size': 0,
            'compression_ratio': 0
        }

def compress_all_videos():
    """加速所有视频文件"""
    
    print("="*80)
    print("视频加速程序")
    print("="*80)
    
    # 检查ffmpeg
    if not check_ffmpeg():
        print("请先安装ffmpeg:")
        print("macOS: brew install ffmpeg")
        print("Ubuntu: sudo apt install ffmpeg")
        print("Windows: 下载ffmpeg并添加到PATH")
        return
    
    # 创建输出文件夹
    output_folder = create_output_folder()
    
    # 获取所有mp4文件
    video_files = glob.glob("videos/*.mp4")
    
    if not video_files:
        print("videos文件夹中没有找到mp4文件")
        return
    
    print(f"\n找到 {len(video_files)} 个mp4文件")
    
    # 询问加速设置
    print(f"\n加速设置:")
    print(f"1. 2倍速 (默认)")
    print(f"2. 1.5倍速")
    print(f"3. 3倍速")
    print(f"4. 测试模式 (只处理前5个文件)")
    
    choice = input("请选择 (1-4): ").strip()
    
    speed_factor = 2.0  # 默认2倍速
    if choice == "1":
        max_workers = 4
        test_mode = False
    elif choice == "2":
        speed_factor = 1.5
        max_workers = 2
        test_mode = False
    elif choice == "3":
        speed_factor = 3.0
        max_workers = 1
        test_mode = False
    elif choice == "4":
        max_workers = 2
        test_mode = True
        video_files = video_files[:5]
    else:
        print("无效选择，使用默认2倍速")
        max_workers = 2
        test_mode = False
    
    print(f"\n开始压缩 {len(video_files)} 个文件...")
    print(f"并发数: {max_workers}")
    print(f"输出文件夹: {output_folder}")
    
    # 准备任务参数
    tasks = [(video_file, output_folder, i+1, len(video_files), speed_factor) 
             for i, video_file in enumerate(video_files)]
    
    # 统计变量
    success_count = 0
    failed_count = 0
    skipped_count = 0
    total_original_size = 0
    total_compressed_size = 0
    
    start_time = time.time()
    
    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_task = {executor.submit(compress_video_worker, task): task for task in tasks}
        
        # 处理完成的任务
        for future in as_completed(future_to_task):
            result = future.result()
            
            if result['status'] == 'success':
                success_count += 1
                print(f"  ✅ {result['filename']} - 压缩率: {result['compression_ratio']:.1f}%")
            elif result['status'] == 'skipped':
                skipped_count += 1
                print(f"  ⏭️  {result['filename']} - 已存在")
            else:
                failed_count += 1
                print(f"  ❌ {result['filename']} - {result['message']}")
            
            total_original_size += result['original_size']
            total_compressed_size += result['compressed_size']
            
            # 显示进度
            completed = success_count + failed_count + skipped_count
            if completed % 10 == 0 or completed == len(video_files):
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                remaining = len(video_files) - completed
                eta = remaining / rate if rate > 0 else 0
                
                print(f"\n进度: {completed}/{len(video_files)} - "
                      f"成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count}")
                print(f"速度: {rate:.1f} 文件/秒, 预计剩余: {eta/60:.1f} 分钟")
    
    # 最终统计
    elapsed_time = time.time() - start_time
    overall_compression = (1 - total_compressed_size / total_original_size) * 100 if total_original_size > 0 else 0
    
    print(f"\n" + "="*80)
    print("压缩完成统计")
    print("="*80)
    print(f"总文件数: {len(video_files)}")
    print(f"压缩成功: {success_count}")
    print(f"压缩失败: {failed_count}")
    print(f"跳过文件: {skipped_count}")
    print(f"总耗时: {elapsed_time/60:.1f} 分钟")
    print(f"平均速度: {len(video_files)/elapsed_time:.1f} 文件/秒")
    
    print(f"\n文件大小统计:")
    print(f"原始总大小: {total_original_size/1024/1024:.1f} MB")
    print(f"压缩后总大小: {total_compressed_size/1024/1024:.1f} MB")
    print(f"节省空间: {(total_original_size-total_compressed_size)/1024/1024:.1f} MB")
    print(f"整体压缩率: {overall_compression:.1f}%")
    
    print(f"\n输出文件夹: {output_folder}")

if __name__ == "__main__":
    compress_all_videos()
