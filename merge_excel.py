#!/usr/bin/env python3
"""
Excel文件合并工具
根据PRD要求合并excel文件夹下的所有Excel文件
"""

import pandas as pd
import os
import glob
import re
from urllib.parse import urlparse

def extract_game_slug_from_url(url):
    """从URL中提取游戏slug"""
    if pd.isna(url) or not url:
        return None

    # 从URL中提取游戏名称
    # 例如: https://1games.io/big-block-blast -> big-block-blast
    try:
        path = urlparse(url).path
        game_slug = path.strip('/').split('/')[-1]
        return game_slug if game_slug else None
    except:
        return None

def generate_image_url(title_url, existing_image_url=None):
    """根据标题链接生成图片URL"""
    if not pd.isna(existing_image_url) and existing_image_url:
        return existing_image_url

    game_slug = extract_game_slug_from_url(title_url)
    if not game_slug:
        return None

    # 生成标准图片URL格式
    base_url = "https://images.1games.io/cache/game"
    image_url = f"{base_url}/{game_slug}/{game_slug}-m200x130.webp"
    return image_url

def extract_tag_from_filename(filename):
    """从文件名中提取标签"""
    # 移除路径和扩展名
    basename = os.path.basename(filename)
    tag = os.path.splitext(basename)[0]

    # 移除 "Games" 后缀和数字后缀
    tag = re.sub(r'\s*Games(\(\d+\))?\s*$', '', tag, flags=re.IGNORECASE)
    tag = tag.strip()

    return tag if tag else basename

def process_excel_file(filepath):
    """处理单个Excel文件"""
    try:
        print(f"处理文件: {filepath}")
        df = pd.read_excel(filepath)

        # 1. 过滤空标题行
        original_count = len(df)
        df = df.dropna(subset=['标题'])
        df = df[df['标题'].str.strip() != '']
        filtered_count = len(df)
        print(f"  过滤空标题: {original_count} -> {filtered_count} 行")

        # 2. 添加标签列
        tag = extract_tag_from_filename(filepath)
        df['标签'] = tag
        print(f"  添加标签: {tag}")

        # 3. 补齐图片URL
        if '图片' in df.columns and '标题链接' in df.columns:
            original_empty = df['图片'].isna().sum()
            df['图片'] = df.apply(lambda row: generate_image_url(row['标题链接'], row['图片']), axis=1)
            new_empty = df['图片'].isna().sum()
            print(f"  补齐图片: {original_empty} -> {new_empty} 个空值")

        return df

    except Exception as e:
        print(f"  错误: {e}")
        return None

def merge_excel_files():
    """按指定顺序合并所有Excel文件"""
    excel_folder = "excel"
    all_excel_files = glob.glob(os.path.join(excel_folder, "*.xlsx"))

    # 按照要求的顺序排序文件
    tag_files = []      # tag开头的文件
    other_files = []    # 其他文件
    priority_files = [] # Popular Games和New Games

    for file in all_excel_files:
        basename = os.path.basename(file)
        if basename.startswith('tag '):
            tag_files.append(file)
        elif 'Popular Games' in basename or 'New Games' in basename:
            priority_files.append(file)
        else:
            other_files.append(file)

    # 排序确保一致性
    tag_files.sort()
    other_files.sort()
    priority_files.sort()

    # 合并顺序：tag文件 -> 其他文件 -> Popular/New Games
    excel_files = tag_files + other_files + priority_files

    print(f"找到 {len(all_excel_files)} 个Excel文件")
    print("合并顺序:")
    print("1. Tag类别文件:")
    for i, file in enumerate(tag_files, 1):
        print(f"   {i}. {os.path.basename(file)}")
    print("2. 其他类别文件:")
    for i, file in enumerate(other_files, 1):
        print(f"   {i}. {os.path.basename(file)}")
    print("3. 优先级文件 (最后合并):")
    for i, file in enumerate(priority_files, 1):
        print(f"   {i}. {os.path.basename(file)}")
    print("="*50)

    all_dataframes = []
    total_rows = 0

    # 处理每个文件
    for excel_file in excel_files:
        df = process_excel_file(excel_file)
        if df is not None and not df.empty:
            all_dataframes.append(df)
            total_rows += len(df)
            print(f"  成功处理: {len(df)} 行数据")
        print("-" * 30)

    if not all_dataframes:
        print("没有有效的数据可以合并")
        return

    # 合并所有数据
    print("合并数据...")
    merged_df = pd.concat(all_dataframes, ignore_index=True)
    print(f"合并后总行数: {len(merged_df)}")

    # 4. 根据标题去重
    original_count = len(merged_df)
    merged_df = merged_df.drop_duplicates(subset=['标题'], keep='first')
    final_count = len(merged_df)
    print(f"去重后行数: {original_count} -> {final_count}")

    # 保存结果
    output_file = "merged_games.xlsx"
    merged_df.to_excel(output_file, index=False)
    print(f"\n合并完成! 输出文件: {output_file}")
    print(f"最终数据: {final_count} 行, {len(merged_df.columns)} 列")

    # 显示统计信息
    print("\n统计信息:")
    print(f"- 总游戏数: {final_count}")
    print(f"- 标签分布:")
    tag_counts = merged_df['标签'].value_counts()
    for tag, count in tag_counts.items():
        print(f"  {tag}: {count}")

    return merged_df

if __name__ == "__main__":
    merge_excel_files()
