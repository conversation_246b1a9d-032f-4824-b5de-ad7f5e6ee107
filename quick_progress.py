#!/usr/bin/env python3
"""
快速检查下载进度
"""

import os
import time

def quick_progress_check():
    """快速检查下载进度"""
    
    if os.path.exists('videos'):
        video_files = [f for f in os.listdir('videos') if f.lower().endswith('.mp4')]
        total_size = sum(os.path.getsize(os.path.join('videos', f)) for f in video_files) / 1024 / 1024
        
        # 计算进度
        original_count = 159  # 原始视频文件数
        target_count = 785 + 159  # 目标总数
        current_count = len(video_files)
        new_downloads = current_count - original_count
        
        progress = new_downloads / 785 * 100 if new_downloads > 0 else 0
        
        print(f"📊 当前进度:")
        print(f"   - 视频文件总数: {current_count}")
        print(f"   - 新下载文件: {new_downloads}/785")
        print(f"   - 完成进度: {progress:.1f}%")
        print(f"   - 文件夹大小: {total_size:.1f} MB")
        print(f"   - 剩余文件: {785 - new_downloads}")
        
        # 估算剩余时间（假设每分钟下载6个文件）
        if new_downloads > 0:
            remaining = 785 - new_downloads
            eta_minutes = remaining / 6  # 假设速度
            eta_hours = eta_minutes / 60
            print(f"   - 预计剩余时间: {eta_hours:.1f} 小时")
    else:
        print("videos文件夹不存在")

if __name__ == "__main__":
    quick_progress_check()
