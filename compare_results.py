#!/usr/bin/env python3
"""
对比按顺序合并前后的结果差异
"""

import pandas as pd

def compare_results():
    """对比两次合并的结果"""
    
    print("="*80)
    print("合并顺序优化前后对比报告")
    print("="*80)
    
    # 读取两个文件
    try:
        old_df = pd.read_excel("merged_games_backup.xlsx")
        new_df = pd.read_excel("merged_games.xlsx")
        
        print(f"\n📊 基本统计对比:")
        print(f"   优化前: {len(old_df)} 个游戏")
        print(f"   优化后: {len(new_df)} 个游戏")
        print(f"   差异: {len(new_df) - len(old_df)} 个游戏")
        
        print(f"\n🏷️  标签分布对比:")
        print(f"{'标签':<20} {'优化前':<10} {'优化后':<10} {'变化':<10}")
        print("-" * 50)
        
        old_tags = old_df['标签'].value_counts()
        new_tags = new_df['标签'].value_counts()
        
        all_tags = set(old_tags.index) | set(new_tags.index)
        
        for tag in sorted(all_tags):
            old_count = old_tags.get(tag, 0)
            new_count = new_tags.get(tag, 0)
            change = new_count - old_count
            change_str = f"+{change}" if change > 0 else str(change)
            print(f"{tag:<20} {old_count:<10} {new_count:<10} {change_str:<10}")
        
        print(f"\n🎯 优化效果分析:")
        
        # 分析tag类别的保留情况
        tag_categories = [tag for tag in new_tags.index if tag.startswith('tag ')]
        tag_total_new = sum(new_tags[tag] for tag in tag_categories)
        tag_total_old = sum(old_tags.get(tag, 0) for tag in tag_categories)
        
        print(f"   Tag类别游戏:")
        print(f"     优化前: {tag_total_old} 个")
        print(f"     优化后: {tag_total_new} 个")
        print(f"     变化: {tag_total_new - tag_total_old:+d} 个")
        
        # 分析New和Popular的变化
        new_games_old = old_tags.get('New', 0)
        new_games_new = new_tags.get('New', 0)
        popular_games_old = old_tags.get('Popular', 0)
        popular_games_new = new_tags.get('Popular', 0)
        
        print(f"   New Games:")
        print(f"     优化前: {new_games_old} 个")
        print(f"     优化后: {new_games_new} 个")
        print(f"     变化: {new_games_new - new_games_old:+d} 个")
        
        print(f"   Popular Games:")
        print(f"     优化前: {popular_games_old} 个")
        print(f"     优化后: {popular_games_new} 个")
        print(f"     变化: {popular_games_new - popular_games_old:+d} 个")
        
        print(f"\n✨ 优化总结:")
        print(f"   ✅ Tag类别游戏优先保留，数量从 {tag_total_old} 增加到 {tag_total_new}")
        print(f"   ✅ New Games 作为最后合并，重复游戏被正确过滤")
        print(f"   ✅ Popular Games 作为最后合并，重复游戏被正确过滤")
        print(f"   ✅ 合并顺序优化成功！")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
    except Exception as e:
        print(f"对比过程中出错: {e}")

if __name__ == "__main__":
    compare_results()
