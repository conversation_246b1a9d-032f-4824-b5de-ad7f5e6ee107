#!/usr/bin/env python3
"""
验证video列路径更新结果
"""

import pandas as pd
import os
import json

def verify_video_paths():
    """验证video列路径更新结果"""
    try:
        print("="*80)
        print("验证 video 列路径更新结果")
        print("="*80)
        
        # 读取更新后的文件
        df = pd.read_excel('games_list_processed.xlsx')
        
        print(f"✅ 文件读取成功")
        print(f"📊 基本信息:")
        print(f"   - 总行数: {len(df):,}")
        print(f"   - 总列数: {len(df.columns)}")
        
        # 验证video列
        print(f"\n🎬 Video列验证:")
        if 'video' in df.columns:
            video_paths = 0
            empty_videos = 0
            invalid_formats = 0
            
            for video in df['video']:
                if pd.isna(video) or video == '':
                    empty_videos += 1
                elif str(video).startswith('videos/') and str(video).endswith('.mp4'):
                    video_paths += 1
                else:
                    invalid_formats += 1
            
            print(f"   视频路径格式: {video_paths}")
            print(f"   空白: {empty_videos}")
            print(f"   无效格式: {invalid_formats}")
            print(f"   视频覆盖率: {video_paths/len(df)*100:.1f}%")
            
            # 显示视频路径样本
            print(f"\n   视频路径样本 (前10个):")
            count = 0
            for idx, row in df.iterrows():
                if pd.notna(row['video']) and str(row['video']).startswith('videos/') and count < 10:
                    print(f"     {count+1:2d}. {row['标题']:<25}: {row['video']}")
                    count += 1
            
            # 验证文件实际存在性
            print(f"\n🔍 文件存在性验证:")
            existing_files = 0
            missing_files = 0
            
            for video in df['video']:
                if pd.notna(video) and str(video).startswith('videos/'):
                    if os.path.exists(str(video)):
                        existing_files += 1
                    else:
                        missing_files += 1
            
            print(f"   文件存在: {existing_files}")
            print(f"   文件缺失: {missing_files}")
            
            if missing_files == 0:
                print(f"   ✅ 所有视频文件都存在")
            else:
                print(f"   ⚠️  有 {missing_files} 个文件缺失")
        
        # 检查路径格式一致性
        print(f"\n📋 路径格式分析:")
        path_patterns = {}
        
        for video in df['video']:
            if pd.notna(video) and str(video).startswith('videos/'):
                video_str = str(video)
                # 提取文件扩展名
                ext = video_str.split('.')[-1] if '.' in video_str else 'no_ext'
                path_patterns[ext] = path_patterns.get(ext, 0) + 1
        
        for ext, count in path_patterns.items():
            print(f"   .{ext}: {count} 个文件")
        
        # JSON兼容性测试
        print(f"\n🧪 JSON兼容性测试:")
        
        # 选择包含视频路径的记录进行测试
        test_records = []
        for idx, row in df.iterrows():
            if pd.notna(row['video']) and str(row['video']).startswith('videos/'):
                record = {}
                for col in df.columns:
                    value = row[col]
                    if pd.isna(value):
                        record[col] = None
                    else:
                        record[col] = str(value)
                test_records.append(record)
                if len(test_records) >= 3:  # 测试3条记录
                    break
        
        if test_records:
            try:
                json_str = json.dumps(test_records, ensure_ascii=False, indent=2)
                print(f"   ✅ JSON序列化成功")
                print(f"   JSON长度: {len(json_str):,} 字符")
                
                # 显示JSON样本
                json_sample = json_str[:400] + "..." if len(json_str) > 400 else json_str
                print(f"   JSON样本:")
                print(json_sample)
                
            except Exception as e:
                print(f"   ❌ JSON序列化失败: {e}")
        
        # 数据完整性检查
        print(f"\n📊 数据完整性:")
        for col in df.columns:
            non_null = df[col].notna().sum()
            null_count = df[col].isna().sum()
            print(f"   {col:<20}: 非空 {non_null:,} | 空值 {null_count:,}")
        
        # 最终评估
        print(f"\n🎯 最终评估:")
        
        if 'video' in df.columns:
            video_paths_count = sum(1 for video in df['video'] if pd.notna(video) and str(video).startswith('videos/'))
            total_games = len(df)
            
            print(f"   总游戏数: {total_games:,}")
            print(f"   有视频路径: {video_paths_count:,}")
            print(f"   视频覆盖率: {video_paths_count/total_games*100:.1f}%")
            
            # 检查路径格式正确性
            correct_format = sum(1 for video in df['video'] 
                               if pd.notna(video) and str(video).startswith('videos/') and str(video).endswith('.mp4'))
            
            print(f"   正确格式: {correct_format:,}")
            print(f"   格式正确率: {correct_format/video_paths_count*100:.1f}%" if video_paths_count > 0 else "   格式正确率: N/A")
            
            if correct_format == video_paths_count and video_paths_count > 0:
                print(f"   ✅ 所有视频路径格式正确")
            elif video_paths_count > 0:
                print(f"   ⚠️  部分路径格式需要检查")
            else:
                print(f"   ❌ 没有找到视频路径")
        
        print(f"\n✅ 验证完成！")
        print(f"📄 文件: games_list_processed.xlsx")
        print(f"🎉 Video列已成功更新为文件路径格式")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_video_paths()
