#!/usr/bin/env python3
"""
根据videos文件夹中的mp4文件更新Excel中的video列
"""

import pandas as pd
import os
import glob
import re
from urllib.parse import urlparse

def get_video_files():
    """获取videos文件夹中的所有视频文件"""
    videos_folder = "videos"
    if not os.path.exists(videos_folder):
        return []
    
    video_files = glob.glob(os.path.join(videos_folder, "*.mp4"))
    # 返回文件名（不含路径）
    return [os.path.basename(f) for f in video_files]

def extract_game_slug_from_url(url):
    """从URL中提取游戏slug"""
    if pd.isna(url) or not url:
        return None
    
    try:
        path = urlparse(url).path
        game_slug = path.strip('/').split('/')[-1]
        return game_slug if game_slug else None
    except:
        return None

def normalize_name(name):
    """标准化名称，用于匹配"""
    if not name:
        return ""
    
    # 转换为小写
    name = name.lower()
    # 移除特殊字符，只保留字母数字和连字符
    name = re.sub(r'[^a-z0-9\-\s]', '', name)
    # 将空格转换为连字符
    name = re.sub(r'\s+', '-', name)
    # 移除多余的连字符
    name = re.sub(r'-+', '-', name)
    # 移除首尾连字符
    name = name.strip('-')
    
    return name

def find_matching_videos(title, title_url, video_files):
    """为游戏标题找到匹配的视频文件"""
    matches = []
    
    # 1. 从URL提取slug进行精确匹配
    game_slug = extract_game_slug_from_url(title_url)
    if game_slug:
        # 精确匹配
        exact_match = f"{game_slug}.mp4"
        if exact_match in video_files:
            matches.append(exact_match)
        
        # 部分匹配（包含slug的文件）
        for video_file in video_files:
            video_name = os.path.splitext(video_file)[0]
            if game_slug in video_name and exact_match not in matches:
                matches.append(video_file)
    
    # 2. 基于标题的模糊匹配
    normalized_title = normalize_name(title)
    
    for video_file in video_files:
        video_name = os.path.splitext(video_file)[0]
        normalized_video = normalize_name(video_name)
        
        # 跳过已经匹配的文件
        if video_file in matches:
            continue
        
        # 检查标题关键词匹配
        title_words = normalized_title.split('-')
        video_words = normalized_video.split('-')
        
        # 计算匹配的单词数
        matching_words = 0
        for word in title_words:
            if len(word) > 2 and word in video_words:  # 忽略太短的词
                matching_words += 1
        
        # 如果有足够的匹配词，认为是匹配的
        if matching_words >= 2 or (matching_words >= 1 and len(title_words) <= 2):
            matches.append(video_file)
        
        # 检查包含关系
        elif normalized_title in normalized_video or normalized_video in normalized_title:
            matches.append(video_file)
    
    return matches

def update_video_column():
    """更新Excel中的video列"""
    
    print("="*80)
    print("更新Video列程序")
    print("="*80)
    
    # 读取Excel文件
    try:
        df = pd.read_excel('merged_games.xlsx')
        print(f"读取Excel文件成功: {len(df)} 个游戏")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return
    
    # 获取视频文件列表
    video_files = get_video_files()
    print(f"找到 {len(video_files)} 个视频文件")
    
    if not video_files:
        print("videos文件夹中没有找到视频文件")
        return
    
    # 显示一些视频文件示例
    print(f"\n视频文件示例:")
    for i, video in enumerate(video_files[:10], 1):
        print(f"  {i:2d}. {video}")
    if len(video_files) > 10:
        print(f"  ... 还有 {len(video_files) - 10} 个文件")
    
    # 添加新的状态列
    if 'video_local_status' not in df.columns:
        df['video_local_status'] = ''
    
    # 统计变量
    updated_count = 0
    matched_count = 0
    multiple_matches = 0
    
    print(f"\n开始匹配视频文件...")
    
    for idx, row in df.iterrows():
        title = row['标题']
        title_url = row['标题链接']
        current_video = row.get('video', '')
        
        # 查找匹配的视频文件
        matches = find_matching_videos(title, title_url, video_files)
        
        if matches:
            matched_count += 1
            
            if len(matches) == 1:
                # 单个匹配
                video_path = f"videos/{matches[0]}"
                df.at[idx, 'video'] = video_path
                df.at[idx, 'video_local_status'] = '本地文件匹配'
                updated_count += 1
                print(f"✅ {title} -> {matches[0]}")
                
            else:
                # 多个匹配
                multiple_matches += 1
                # 选择第一个匹配，或者可以选择最相似的
                video_path = f"videos/{matches[0]}"
                df.at[idx, 'video'] = video_path
                df.at[idx, 'video_local_status'] = f'多个匹配({len(matches)}个)'
                updated_count += 1
                print(f"🔄 {title} -> {matches[0]} (还有{len(matches)-1}个匹配)")
        else:
            # 没有匹配
            df.at[idx, 'video_local_status'] = '无匹配本地文件'
    
    # 保存更新后的Excel文件
    try:
        df.to_excel('merged_games.xlsx', index=False)
        print(f"\n✅ Excel文件已更新")
    except Exception as e:
        print(f"❌ 保存Excel文件失败: {e}")
        return
    
    # 统计结果
    print(f"\n" + "="*80)
    print("更新完成统计")
    print("="*80)
    print(f"总游戏数: {len(df)}")
    print(f"找到匹配的游戏: {matched_count}")
    print(f"更新video列: {updated_count}")
    print(f"多个匹配的游戏: {multiple_matches}")
    print(f"匹配率: {matched_count/len(df)*100:.1f}%")
    
    # 显示匹配统计
    if 'video_local_status' in df.columns:
        status_counts = df['video_local_status'].value_counts()
        print(f"\n本地文件匹配状态:")
        for status, count in status_counts.items():
            if status:  # 只显示非空状态
                print(f"  {status}: {count}")
    
    return df

def analyze_unmatched_videos():
    """分析未匹配的视频文件"""
    print(f"\n" + "="*60)
    print("未匹配视频分析")
    print("="*60)
    
    try:
        df = pd.read_excel('merged_games.xlsx')
        video_files = get_video_files()
        
        # 找出已匹配的视频文件
        matched_videos = set()
        for _, row in df.iterrows():
            video = row.get('video', '')
            if isinstance(video, str) and video.startswith('videos/'):
                video_file = video.replace('videos/', '')
                matched_videos.add(video_file)
        
        # 找出未匹配的视频文件
        unmatched_videos = set(video_files) - matched_videos
        
        print(f"总视频文件: {len(video_files)}")
        print(f"已匹配: {len(matched_videos)}")
        print(f"未匹配: {len(unmatched_videos)}")
        
        if unmatched_videos:
            print(f"\n未匹配的视频文件:")
            for i, video in enumerate(sorted(unmatched_videos)[:20], 1):
                print(f"  {i:2d}. {video}")
            if len(unmatched_videos) > 20:
                print(f"  ... 还有 {len(unmatched_videos) - 20} 个文件")
    
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    df = update_video_column()
    if df is not None:
        analyze_unmatched_videos()
