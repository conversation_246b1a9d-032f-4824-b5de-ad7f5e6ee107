#!/usr/bin/env python3
"""
处理 merged_games_list.xlsx 的简介列，使其适合JSON格式
1. 将分行用 \n 或 \n\n 代替
2. 移除超链接，保留链接文本
"""

import pandas as pd
import re

def process_intro_for_json(intro_text):
    """
    处理简介文本，使其适合JSON格式
    """
    if pd.isna(intro_text) or not intro_text:
        return ""

    text = str(intro_text)

    # 1. 移除超链接，保留链接文本
    # 处理 [文本](链接) 格式
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)

    # 处理其他可能的链接格式
    text = re.sub(r'<([^>]+)>', r'\1', text)

    # 2. 处理双引号，转义为 \"
    text = text.replace('"', '\\"')

    # 3. 处理分行
    # 将多个连续换行符替换为 \\n\\n
    text = re.sub(r'\n\s*\n\s*\n+', '\\n\\n', text)

    # 将单个换行符替换为 \\n
    text = re.sub(r'\n', '\\n', text)

    # 4. 清理多余的空格
    text = re.sub(r'[ \t]+', ' ', text)

    # 5. 清理开头和结尾的空白
    text = text.strip()

    return text

def process_merged_games_for_json():
    """处理合并后的游戏列表文件，使简介适合JSON格式"""
    
    print("="*80)
    print("处理简介列，使其适合JSON格式")
    print("="*80)
    
    try:
        # 读取文件
        print("📖 读取文件...")
        df = pd.read_excel('merged_games_list.xlsx')
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        
        # 检查简介列
        if '简介' not in df.columns:
            print("❌ 未找到简介列")
            return
        
        print(f"\n🔄 处理简介列...")
        
        # 显示处理前的样本
        print("处理前简介样本:")
        for i in range(min(2, len(df))):
            if pd.notna(df.iloc[i]['简介']):
                sample = str(df.iloc[i]['简介'])[:150] + "..." if len(str(df.iloc[i]['简介'])) > 150 else str(df.iloc[i]['简介'])
                print(f"   第{i+1}行: {sample}")
        
        # 处理简介列
        df['简介_processed'] = df['简介'].apply(process_intro_for_json)
        
        # 显示处理后的样本
        print("\n处理后简介样本:")
        for i in range(min(2, len(df))):
            if pd.notna(df.iloc[i]['简介_processed']):
                sample = str(df.iloc[i]['简介_processed'])[:150] + "..." if len(str(df.iloc[i]['简介_processed'])) > 150 else str(df.iloc[i]['简介_processed'])
                print(f"   第{i+1}行: {sample}")
        
        # 统计处理结果
        original_empty = df['简介'].isna().sum()
        processed_empty = df['简介_processed'].eq('').sum()
        
        # 统计包含换行符的数量
        has_newlines = df['简介_processed'].str.contains('\\\\n', na=False).sum()
        has_links_before = df['简介'].str.contains(r'\[.*?\]\(.*?\)', na=False).sum()
        has_links_after = df['简介_processed'].str.contains(r'\[.*?\]\(.*?\)', na=False).sum()
        
        print(f"\n📊 处理统计:")
        print(f"   原始空值: {original_empty}")
        print(f"   处理后空值: {processed_empty}")
        print(f"   成功处理: {len(df) - processed_empty}")
        print(f"   包含换行符的简介: {has_newlines}")
        print(f"   处理前包含链接: {has_links_before}")
        print(f"   处理后包含链接: {has_links_after}")
        print(f"   链接移除成功率: {(has_links_before - has_links_after)/max(has_links_before, 1)*100:.1f}%")
        
        # 替换原列
        print(f"\n🔄 更新简介列...")
        df['简介'] = df['简介_processed']
        
        # 删除临时列
        df = df.drop(['简介_processed'], axis=1)
        
        # 保存处理后的文件
        output_file = 'merged_games_list.xlsx'
        print(f"\n💾 保存到: {output_file}")
        df.to_excel(output_file, index=False)
        
        print(f"\n✅ 处理完成!")
        print(f"   文件: {output_file}")
        print(f"   总行数: {len(df)}")
        print(f"   总列数: {len(df.columns)}")
        
        # 显示最终样本
        print(f"\n📝 最终样本数据:")
        for i in range(min(2, len(df))):
            print(f"\n   === 第 {i+1} 条记录 ===")
            print(f"   标题: {df.iloc[i]['标题']}")
            if pd.notna(df.iloc[i]['简介']):
                intro_sample = str(df.iloc[i]['简介'])[:200] + "..." if len(str(df.iloc[i]['简介'])) > 200 else str(df.iloc[i]['简介'])
                print(f"   简介: {intro_sample}")
        
        # 验证JSON兼容性
        print(f"\n🔍 JSON兼容性检查:")
        
        # 检查是否还有真实的换行符
        real_newlines = df['简介'].str.contains('\n', na=False).sum()
        print(f"   包含真实换行符的简介: {real_newlines}")
        
        # 检查是否还有超链接
        remaining_links = df['简介'].str.contains(r'\[.*?\]\(.*?\)', na=False).sum()
        print(f"   仍包含超链接的简介: {remaining_links}")
        
        # 检查特殊字符
        has_quotes = df['简介'].str.contains('"', na=False).sum()
        print(f"   包含双引号的简介: {has_quotes}")
        
        if real_newlines == 0 and remaining_links == 0:
            print(f"   ✅ JSON兼容性: 优秀")
        else:
            print(f"   ⚠️  JSON兼容性: 需要注意")
        
        return df
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_merged_games_for_json()
