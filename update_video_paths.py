#!/usr/bin/env python3
"""
更新 games_list_processed.xlsx 中的 video 列
将"存在"修改为实际的视频文件路径 "videos/xxx.mp4"
"""

import pandas as pd
import os
import glob
import re
from urllib.parse import urlparse

def extract_game_slug_from_url(url):
    """从URL中提取游戏slug"""
    if pd.isna(url) or not url:
        return None
    
    try:
        path = urlparse(url).path
        game_slug = path.strip('/').split('/')[-1]
        return game_slug if game_slug else None
    except:
        return None

def normalize_title_for_matching(title):
    """标准化标题用于文件名匹配"""
    if pd.isna(title) or not title:
        return ""
    
    # 转换为小写
    title = str(title).lower()
    # 移除特殊字符，只保留字母数字和连字符
    title = re.sub(r'[^a-z0-9\-\s]', '', title)
    # 将空格转换为连字符
    title = re.sub(r'\s+', '-', title)
    # 移除多余的连字符
    title = re.sub(r'-+', '-', title)
    # 移除首尾连字符
    title = title.strip('-')
    
    return title

def find_video_file(title, title_url, video_files):
    """
    查找对应的视频文件
    """
    if not video_files:
        return None
    
    # 方法1: 从URL提取slug进行精确匹配
    game_slug = extract_game_slug_from_url(title_url)
    if game_slug:
        exact_match = f"{game_slug}.mp4"
        if exact_match in video_files:
            return f"videos/{exact_match}"
        
        # 部分匹配
        for video_file in video_files:
            if game_slug in video_file:
                return f"videos/{video_file}"
    
    # 方法2: 标准化标题进行匹配
    normalized_title = normalize_title_for_matching(title)
    if normalized_title:
        exact_match = f"{normalized_title}.mp4"
        if exact_match in video_files:
            return f"videos/{exact_match}"
        
        # 部分匹配
        for video_file in video_files:
            video_name = os.path.splitext(video_file)[0]
            if normalized_title in video_name or video_name in normalized_title:
                return f"videos/{video_file}"
    
    return None

def get_video_files():
    """获取videos文件夹中的所有视频文件"""
    video_folder = "videos"
    if not os.path.exists(video_folder):
        return []
    
    # 获取所有视频文件
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.webm']
    video_files = []
    
    for ext in video_extensions:
        files = glob.glob(os.path.join(video_folder, ext))
        video_files.extend([os.path.basename(f) for f in files])
    
    return video_files

def update_video_paths():
    """更新video列的路径"""
    
    print("="*80)
    print("更新 video 列路径")
    print("="*80)
    
    try:
        # 读取文件
        print("📖 读取文件...")
        df = pd.read_excel('games_list_processed.xlsx')
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        
        # 获取视频文件列表
        print(f"\n📁 扫描视频文件...")
        video_files = get_video_files()
        print(f"   找到 {len(video_files)} 个视频文件")
        if len(video_files) > 0:
            print(f"   视频文件示例: {video_files[:5]}")
        
        # 检查必要的列
        if 'video' not in df.columns:
            print("❌ 未找到video列")
            return
        
        if '标题' not in df.columns:
            print("❌ 未找到标题列")
            return
        
        title_url_col = None
        for col in ['标题链接', 'url', 'link']:
            if col in df.columns:
                title_url_col = col
                break
        
        if not title_url_col:
            print("⚠️  未找到标题链接列，将仅使用标题进行匹配")
        
        print(f"\n🔄 更新video列路径...")
        
        # 统计当前状态
        exists_count = sum(1 for video in df['video'] if str(video) == '存在')
        print(f"   当前标记为'存在'的记录: {exists_count}")
        
        # 更新video列
        updated_count = 0
        not_found_count = 0
        
        for idx, row in df.iterrows():
            if str(row['video']) == '存在':
                title = row['标题']
                title_url = row[title_url_col] if title_url_col else ""
                
                video_path = find_video_file(title, title_url, video_files)
                
                if video_path:
                    df.at[idx, 'video'] = video_path
                    updated_count += 1
                else:
                    # 如果找不到对应文件，保留空白
                    df.at[idx, 'video'] = ""
                    not_found_count += 1
        
        print(f"   成功更新: {updated_count} 条记录")
        print(f"   未找到文件: {not_found_count} 条记录")
        
        # 显示更新后的样本
        print(f"\n📝 更新后的样本:")
        count = 0
        for idx, row in df.iterrows():
            if pd.notna(row['video']) and str(row['video']).startswith('videos/') and count < 5:
                print(f"   {count+1}. {row['标题']}: {row['video']}")
                count += 1
        
        # 保存更新后的文件
        output_file = 'games_list_processed.xlsx'
        print(f"\n💾 保存到: {output_file}")
        df.to_excel(output_file, index=False)
        
        print(f"\n✅ 更新完成!")
        print(f"   文件: {output_file}")
        print(f"   总行数: {len(df)}")
        
        # 最终统计
        final_video_paths = sum(1 for video in df['video'] if pd.notna(video) and str(video).startswith('videos/'))
        final_empty = sum(1 for video in df['video'] if pd.isna(video) or video == '')
        
        print(f"\n📊 最终统计:")
        print(f"   视频路径: {final_video_paths} 条记录")
        print(f"   空白: {final_empty} 条记录")
        print(f"   视频覆盖率: {final_video_paths/len(df)*100:.1f}%")
        
        # 验证路径格式
        print(f"\n🔍 路径格式验证:")
        valid_paths = 0
        invalid_paths = 0
        
        for video in df['video']:
            if pd.notna(video) and video != '':
                if str(video).startswith('videos/') and str(video).endswith('.mp4'):
                    valid_paths += 1
                else:
                    invalid_paths += 1
        
        print(f"   有效路径格式: {valid_paths}")
        print(f"   无效路径格式: {invalid_paths}")
        
        if invalid_paths == 0:
            print(f"   ✅ 所有路径格式正确")
        else:
            print(f"   ⚠️  存在格式问题")
        
        return df
        
    except Exception as e:
        print(f"❌ 更新过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_video_paths()
