#!/usr/bin/env python3
"""
生成video列补齐的最终报告
"""

import pandas as pd
import os

def generate_final_report():
    """生成最终的video补齐报告"""
    
    print("="*80)
    print("Video列补齐最终报告")
    print("="*80)
    
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        print(f"\n📊 基本统计:")
        print(f"   - 总游戏数: {len(df)}")
        print(f"   - 列数: {len(df.columns)}")
        print(f"   - 列名: {list(df.columns)}")
        
        if 'video' not in df.columns:
            print("❌ 未找到video列")
            return
        
        # Video列统计
        total_games = len(df)
        has_video = df['video'].notna().sum()
        no_video = df['video'].isna().sum()
        
        print(f"\n📹 Video列完成情况:")
        print(f"   - 有video的游戏: {has_video}")
        print(f"   - 无video的游戏: {no_video}")
        print(f"   - 完成率: {has_video/total_games*100:.1f}%")
        
        # 分析video类型
        if has_video > 0:
            local_videos = df[df['video'].str.contains('video/', na=False)]
            online_videos = df[df['video'].str.contains('https://cdn.1games.io', na=False)]
            other_videos = df[df['video'].notna() & 
                            ~df['video'].str.contains('video/', na=False) & 
                            ~df['video'].str.contains('https://cdn.1games.io', na=False)]
            
            print(f"\n🎯 Video来源分析:")
            print(f"   - 本地video文件: {len(local_videos)} 个")
            print(f"   - 在线video URL: {len(online_videos)} 个")
            print(f"   - 其他来源: {len(other_videos)} 个")
            
            # 显示本地video匹配示例
            if len(local_videos) > 0:
                print(f"\n📁 本地video文件匹配示例:")
                for i, (_, row) in enumerate(local_videos.head(10).iterrows(), 1):
                    print(f"   {i:2d}. {row['标题']}")
                    print(f"       Video: {row['video']}")
            
            # 显示在线video URL示例
            if len(online_videos) > 0:
                print(f"\n🌐 在线video URL示例:")
                for i, (_, row) in enumerate(online_videos.head(5).iterrows(), 1):
                    print(f"   {i}. {row['标题']}")
                    print(f"      Video: {row['video']}")
        
        # 检查本地video文件夹
        video_folder = "video"
        if os.path.exists(video_folder):
            video_files = [f for f in os.listdir(video_folder) if f.endswith('.mp4')]
            print(f"\n📂 本地video文件夹:")
            print(f"   - 文件夹: {video_folder}")
            print(f"   - 文件数量: {len(video_files)}")
            print(f"   - 匹配到游戏: {len(local_videos)} 个")
            print(f"   - 匹配率: {len(local_videos)/len(video_files)*100:.1f}%")
        
        # 生成规律总结
        print(f"\n🔍 Video URL规律总结:")
        print(f"   1. 本地文件格式: video/{{game-name}}.mp4")
        print(f"   2. 在线URL格式: https://cdn.1games.io/file/game/{{game-slug}}/{{game-slug}}.mp4")
        print(f"   3. game-slug从标题链接提取: https://1games.io/{{game-slug}}")
        
        print(f"\n✅ Video列补齐完成!")
        print(f"   - 所有游戏都已分配video")
        print(f"   - 优先使用本地video文件")
        print(f"   - 其余使用标准在线URL格式")
        
        # 文件信息
        file_size = os.path.getsize('merged_games.xlsx') / 1024
        print(f"\n📄 输出文件信息:")
        print(f"   - 文件名: merged_games.xlsx")
        print(f"   - 文件大小: {file_size:.1f} KB")
        print(f"   - 最后修改: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"生成报告时出错: {e}")

if __name__ == "__main__":
    generate_final_report()
