#!/usr/bin/env python3
"""
分析merged_games.xlsx中video列的规律
"""

import pandas as pd
import os
import glob

def analyze_video_column():
    """分析video列的内容和规律"""
    
    # 读取Excel文件
    try:
        df = pd.read_excel('merged_games.xlsx')
        print("="*60)
        print("Video列分析报告")
        print("="*60)
        
        print(f"\n📊 基本信息:")
        print(f"   - 总行数: {len(df)}")
        print(f"   - 列名: {list(df.columns)}")
        
        if 'video' not in df.columns:
            print("❌ 未找到video列")
            return
        
        # 统计video列
        total_videos = len(df)
        non_empty_videos = df['video'].notna().sum()
        empty_videos = df['video'].isna().sum()
        
        print(f"\n📹 Video列统计:")
        print(f"   - 总游戏数: {total_videos}")
        print(f"   - 有video的游戏: {non_empty_videos}")
        print(f"   - 无video的游戏: {empty_videos}")
        print(f"   - 完成率: {non_empty_videos/total_videos*100:.1f}%")
        
        # 显示有video的游戏示例
        print(f"\n📝 有video的游戏示例:")
        video_games = df[df['video'].notna()][['标题', '标题链接', 'video']].head(10)
        for i, (_, row) in enumerate(video_games.iterrows(), 1):
            print(f"   {i}. 标题: {row['标题']}")
            print(f"      链接: {row['标题链接']}")
            print(f"      视频: {row['video']}")
            print()
        
        # 分析video文件名规律
        print(f"\n🔍 Video文件名规律分析:")
        video_files = df[df['video'].notna()]['video'].tolist()
        
        if video_files:
            print(f"   示例video文件:")
            for i, video in enumerate(video_files[:5], 1):
                print(f"   {i}. {video}")
            
            # 分析文件名模式
            print(f"\n   文件名模式分析:")
            extensions = set()
            for video in video_files:
                if '.' in video:
                    ext = video.split('.')[-1]
                    extensions.add(ext)
            print(f"   - 文件扩展名: {list(extensions)}")
            
            # 检查是否有对应的游戏slug
            print(f"\n   与游戏标题的关联分析:")
            for i, (_, row) in enumerate(video_games.head(3).iterrows(), 1):
                title = row['标题']
                video = row['video']
                url = row['标题链接']
                
                # 从URL提取slug
                if url and 'https://1games.io/' in url:
                    slug = url.replace('https://1games.io/', '').strip('/')
                    print(f"   {i}. 标题: {title}")
                    print(f"      URL slug: {slug}")
                    print(f"      Video: {video}")
                    
                    # 检查video文件名是否包含slug
                    if slug in video:
                        print(f"      ✅ Video文件名包含URL slug")
                    else:
                        print(f"      ❌ Video文件名不包含URL slug")
                    print()
        
        return df
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        return None

def check_video_folder():
    """检查video文件夹中的文件"""
    print(f"\n📁 Video文件夹分析:")
    
    video_folder = "video"
    if not os.path.exists(video_folder):
        print(f"   ❌ {video_folder} 文件夹不存在")
        return []
    
    video_files = glob.glob(os.path.join(video_folder, "*"))
    print(f"   - 文件夹路径: {video_folder}")
    print(f"   - 文件数量: {len(video_files)}")
    
    if video_files:
        print(f"   - 文件列表:")
        for i, file in enumerate(sorted(video_files), 1):
            filename = os.path.basename(file)
            print(f"     {i:2d}. {filename}")
    
    return [os.path.basename(f) for f in video_files]

if __name__ == "__main__":
    df = analyze_video_column()
    video_files = check_video_folder()
    
    if df is not None and 'video' in df.columns:
        print(f"\n🎯 规律总结:")
        print(f"   需要分析video列中已有内容的规律")
        print(f"   然后根据规律为其他游戏补齐video文件名")
