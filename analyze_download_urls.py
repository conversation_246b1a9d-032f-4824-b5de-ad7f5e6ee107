#!/usr/bin/env python3
"""
分析merged_games.xlsx中的图片和视频URL，准备下载
"""

import pandas as pd
import os
from urllib.parse import urlparse

def analyze_urls():
    """分析图片和视频URL"""
    
    print("="*80)
    print("图片和视频URL分析报告")
    print("="*80)
    
    try:
        df = pd.read_excel('merged_games.xlsx')
        
        print(f"\n📊 基本信息:")
        print(f"   - 总游戏数: {len(df)}")
        print(f"   - 列名: {list(df.columns)}")
        
        # 分析图片URL
        if '图片' in df.columns:
            print(f"\n🖼️  图片URL分析:")
            image_urls = df['图片'].dropna()
            print(f"   - 有图片URL的游戏: {len(image_urls)}")
            print(f"   - 无图片URL的游戏: {len(df) - len(image_urls)}")
            
            # 分析图片URL域名
            image_domains = {}
            for url in image_urls:
                try:
                    domain = urlparse(url).netloc
                    image_domains[domain] = image_domains.get(domain, 0) + 1
                except:
                    image_domains['解析失败'] = image_domains.get('解析失败', 0) + 1
            
            print(f"   - 图片URL域名分布:")
            for domain, count in image_domains.items():
                print(f"     {domain}: {count} 个")
            
            # 显示图片URL示例
            print(f"   - 图片URL示例:")
            for i, url in enumerate(image_urls.head(5), 1):
                print(f"     {i}. {url}")
        
        # 分析视频URL
        if 'video' in df.columns:
            print(f"\n📹 视频URL分析:")
            video_urls = df['video'].dropna()
            print(f"   - 有视频URL的游戏: {len(video_urls)}")
            print(f"   - 无视频URL的游戏: {len(df) - len(video_urls)}")
            
            # 分析视频URL类型
            local_videos = df[df['video'].str.contains('video/', na=False)]
            online_videos = df[df['video'].str.contains('https://', na=False)]
            
            print(f"   - 本地视频文件: {len(local_videos)} 个")
            print(f"   - 在线视频URL: {len(online_videos)} 个")
            
            # 分析在线视频URL域名
            if len(online_videos) > 0:
                video_domains = {}
                for url in online_videos['video']:
                    try:
                        domain = urlparse(url).netloc
                        video_domains[domain] = video_domains.get(domain, 0) + 1
                    except:
                        video_domains['解析失败'] = video_domains.get('解析失败', 0) + 1
                
                print(f"   - 在线视频URL域名分布:")
                for domain, count in video_domains.items():
                    print(f"     {domain}: {count} 个")
            
            # 显示视频URL示例
            print(f"   - 在线视频URL示例:")
            for i, url in enumerate(online_videos['video'].head(5), 1):
                print(f"     {i}. {url}")
        
        # 检查现有文件夹
        print(f"\n📁 现有文件夹状态:")
        
        images_folder = "images"
        if os.path.exists(images_folder):
            image_files = [f for f in os.listdir(images_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif'))]
            print(f"   - images文件夹: 存在，包含 {len(image_files)} 个图片文件")
        else:
            print(f"   - images文件夹: 不存在")
        
        videos_folder = "videos"
        if os.path.exists(videos_folder):
            video_files = [f for f in os.listdir(videos_folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
            print(f"   - videos文件夹: 存在，包含 {len(video_files)} 个视频文件")
        else:
            print(f"   - videos文件夹: 不存在")
        
        video_folder = "video"
        if os.path.exists(video_folder):
            video_files = [f for f in os.listdir(video_folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
            print(f"   - video文件夹: 存在，包含 {len(video_files)} 个视频文件")
        
        # 下载计划
        print(f"\n📋 下载计划:")
        print(f"   1. 图片下载:")
        print(f"      - 需要下载: {len(image_urls)} 个图片")
        print(f"      - 目标文件夹: images/")
        print(f"      - 支持格式: .webp, .jpg, .png, .gif")
        
        print(f"   2. 视频下载:")
        print(f"      - 需要下载: {len(online_videos)} 个在线视频")
        print(f"      - 目标文件夹: videos/")
        print(f"      - 支持格式: .mp4")
        print(f"      - 本地视频: {len(local_videos)} 个 (无需下载)")
        
        return df
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        return None

if __name__ == "__main__":
    analyze_urls()
