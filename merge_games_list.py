#!/usr/bin/env python3
"""
合并 games_list.xlsx 和 games_list2.xlsx
使用标题作为关联字段，转换为大写进行匹配
"""

import pandas as pd
import os

def merge_games_list():
    """合并两个游戏列表文件"""
    
    print("="*80)
    print("Games List 文件合并程序")
    print("="*80)
    
    # 检查文件是否存在
    file1 = 'games_list.xlsx'
    file2 = 'games_list2.xlsx'
    
    if not os.path.exists(file1):
        print(f"❌ 文件不存在: {file1}")
        return
    
    if not os.path.exists(file2):
        print(f"❌ 文件不存在: {file2}")
        return
    
    try:
        # 读取第一个文件
        print(f"📖 读取文件: {file1}")
        df1 = pd.read_excel(file1)
        print(f"   行数: {len(df1)}")
        print(f"   列数: {len(df1.columns)}")
        print(f"   列名: {list(df1.columns)}")
        
        # 读取第二个文件
        print(f"\n📖 读取文件: {file2}")
        df2 = pd.read_excel(file2)
        print(f"   行数: {len(df2)}")
        print(f"   列数: {len(df2.columns)}")
        print(f"   列名: {list(df2.columns)}")
        
        # 查找标题列
        title_col1 = None
        title_col2 = None
        
        # 查找包含"标题"的列
        for col in df1.columns:
            if '标题' in str(col):
                title_col1 = col
                break
        
        for col in df2.columns:
            if '标题' in str(col):
                title_col2 = col
                break
        
        if not title_col1:
            print(f"❌ 在 {file1} 中未找到标题列")
            return
        
        if not title_col2:
            print(f"❌ 在 {file2} 中未找到标题列")
            return
        
        print(f"\n🔗 使用关联字段:")
        print(f"   {file1}: {title_col1}")
        print(f"   {file2}: {title_col2}")
        
        # 创建大写标题列用于匹配
        print(f"\n🔄 转换标题为大写进行匹配...")
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        # 添加大写标题列
        df1_copy['TITLE_UPPER'] = df1_copy[title_col1].astype(str).str.upper().str.strip()
        df2_copy['TITLE_UPPER'] = df2_copy[title_col2].astype(str).str.upper().str.strip()
        
        # 显示一些样本数据
        print(f"\n📋 {file1} 前3行标题:")
        for i in range(min(3, len(df1_copy))):
            original = df1_copy.iloc[i][title_col1]
            upper = df1_copy.iloc[i]['TITLE_UPPER']
            print(f"   {i+1}. 原始: {original}")
            print(f"      大写: {upper}")
        
        print(f"\n📋 {file2} 前3行标题:")
        for i in range(min(3, len(df2_copy))):
            original = df2_copy.iloc[i][title_col2]
            upper = df2_copy.iloc[i]['TITLE_UPPER']
            print(f"   {i+1}. 原始: {original}")
            print(f"      大写: {upper}")
        
        # 检查匹配情况
        common_titles = set(df1_copy['TITLE_UPPER']) & set(df2_copy['TITLE_UPPER'])
        print(f"\n📊 匹配分析:")
        print(f"   {file1} 唯一标题数: {df1_copy['TITLE_UPPER'].nunique()}")
        print(f"   {file2} 唯一标题数: {df2_copy['TITLE_UPPER'].nunique()}")
        print(f"   匹配的标题数: {len(common_titles)}")
        
        if len(common_titles) > 0:
            print(f"   匹配率: {len(common_titles)/min(df1_copy['TITLE_UPPER'].nunique(), df2_copy['TITLE_UPPER'].nunique())*100:.1f}%")
            
            # 显示一些匹配的标题
            print(f"\n   匹配的标题示例 (前5个):")
            for i, title in enumerate(list(common_titles)[:5], 1):
                print(f"     {i}. {title}")
        
        # 合并数据
        print(f"\n🔄 开始合并...")
        
        # 使用大写标题进行左连接
        merged_df = pd.merge(df1_copy, df2_copy, on='TITLE_UPPER', how='left', suffixes=('', '_from_file2'))
        
        print(f"   合并后行数: {len(merged_df)}")
        print(f"   合并后列数: {len(merged_df.columns)}")
        
        # 检查匹配情况
        matched_count = merged_df[title_col2].notna().sum()
        print(f"   匹配的记录数: {matched_count}")
        print(f"   匹配率: {matched_count/len(df1_copy)*100:.1f}%")
        
        # 处理未匹配的df2数据
        unmatched_df2 = df2_copy[~df2_copy['TITLE_UPPER'].isin(df1_copy['TITLE_UPPER'])].copy()
        if len(unmatched_df2) > 0:
            print(f"   {file2} 中有 {len(unmatched_df2)} 条新记录将被追加")
            
            # 为unmatched_df2添加缺失的列
            for col in merged_df.columns:
                if col not in unmatched_df2.columns:
                    unmatched_df2.loc[:, col] = None
            
            # 重新排列列顺序
            unmatched_df2 = unmatched_df2[merged_df.columns]
            
            # 追加新记录
            merged_df = pd.concat([merged_df, unmatched_df2], ignore_index=True)
            print(f"   追加后总行数: {len(merged_df)}")
        
        # 删除临时的大写标题列
        merged_df = merged_df.drop('TITLE_UPPER', axis=1)
        
        # 保存结果
        output_file = 'merged_games_list.xlsx'
        print(f"\n💾 保存到: {output_file}")
        merged_df.to_excel(output_file, index=False)
        
        print(f"\n✅ 合并完成!")
        print(f"   最终文件: {output_file}")
        print(f"   总行数: {len(merged_df)}")
        print(f"   总列数: {len(merged_df.columns)}")
        
        # 显示列名
        print(f"\n📊 最终列名:")
        for i, col in enumerate(merged_df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # 显示样本数据
        print(f"\n📝 样本数据 (前2条):")
        for i in range(min(2, len(merged_df))):
            print(f"\n   === 第 {i+1} 条记录 ===")
            for col in merged_df.columns:
                value = merged_df.iloc[i][col]
                if pd.isna(value):
                    value = "[空]"
                elif isinstance(value, str) and len(str(value)) > 50:
                    value = str(value)[:47] + "..."
                print(f"   {col}: {value}")
        
        return merged_df
        
    except Exception as e:
        print(f"❌ 合并过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    merge_games_list()
