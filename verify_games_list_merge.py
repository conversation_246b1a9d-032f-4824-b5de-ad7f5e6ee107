#!/usr/bin/env python3
"""
验证 games_list 合并结果
"""

import pandas as pd
import sys

def verify_games_list_merge():
    """验证合并结果"""
    try:
        print("="*80)
        print("Games List 合并结果验证")
        print("="*80)
        
        # 读取合并后的文件
        df = pd.read_excel('merged_games_list.xlsx')
        
        print(f"✅ 文件读取成功")
        print(f"📊 基本信息:")
        print(f"   - 总行数: {len(df):,}")
        print(f"   - 总列数: {len(df.columns)}")
        
        print(f"\n📋 列信息:")
        for i, col in enumerate(df.columns, 1):
            non_null = df[col].notna().sum()
            null_count = df[col].isna().sum()
            print(f"   {i:2d}. {col:<25} | 非空: {non_null:,} | 空值: {null_count:,}")
        
        # 检查关键列
        key_columns = ['标题', '分类', '技术', '平台', '简介']
        print(f"\n🔍 关键列检查:")
        for col in key_columns:
            if col in df.columns:
                non_null = df[col].notna().sum()
                print(f"   ✅ {col}: {non_null:,} 条非空记录")
            else:
                print(f"   ❌ {col}: 列不存在")
        
        # 检查匹配情况
        if '标题_from_file2' in df.columns:
            matched_count = df['标题_from_file2'].notna().sum()
            print(f"\n🔗 匹配情况:")
            print(f"   匹配成功的记录: {matched_count:,}")
            print(f"   匹配率: {matched_count/len(df)*100:.1f}%")
        
        # 检查分类分布
        if '分类' in df.columns:
            print(f"\n📂 分类分布:")
            category_counts = df['分类'].value_counts().head(10)
            for category, count in category_counts.items():
                print(f"   {category:<20}: {count:,}")
        
        # 检查技术分布
        if '技术' in df.columns:
            print(f"\n⚙️  技术分布:")
            tech_counts = df['技术'].value_counts()
            for tech, count in tech_counts.items():
                print(f"   {tech:<20}: {count:,}")
        
        # 检查平台分布
        if '平台' in df.columns:
            print(f"\n💻 平台分布:")
            platform_counts = df['平台'].value_counts()
            for platform, count in platform_counts.items():
                print(f"   {platform:<20}: {count:,}")
        
        # 媒体文件统计
        print(f"\n🎬 媒体文件统计:")
        if 'video' in df.columns:
            has_video = df['video'].notna().sum()
            print(f"   有视频的游戏: {has_video:,} ({has_video/len(df)*100:.1f}%)")
        
        if '图片' in df.columns:
            has_image = df['图片'].notna().sum()
            print(f"   有图片的游戏: {has_image:,} ({has_image/len(df)*100:.1f}%)")
        
        # 评分统计
        if 'card_rating' in df.columns:
            print(f"\n⭐ 评分统计:")
            has_rating = df['card_rating'].notna().sum()
            print(f"   有评分的游戏: {has_rating:,} ({has_rating/len(df)*100:.1f}%)")
            
            if has_rating > 0:
                avg_rating = df['card_rating'].mean()
                min_rating = df['card_rating'].min()
                max_rating = df['card_rating'].max()
                print(f"   平均评分: {avg_rating:.2f}")
                print(f"   评分范围: {min_rating} - {max_rating}")
        
        print(f"\n✅ 验证完成！合并成功。")
        print(f"📄 输出文件: merged_games_list.xlsx")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_games_list_merge()
    sys.exit(0 if success else 1)
