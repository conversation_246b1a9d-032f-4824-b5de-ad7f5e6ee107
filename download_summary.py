#!/usr/bin/env python3
"""
生成下载任务的最终总结报告
"""

import pandas as pd
import os
import glob

def generate_download_summary():
    """生成下载总结报告"""
    
    print("="*80)
    print("媒体文件下载总结报告")
    print("="*80)
    
    # 1. 文件夹统计
    print(f"\n📁 文件夹统计:")
    
    # 统计images文件夹
    if os.path.exists('images'):
        image_files = [f for f in os.listdir('images') if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif'))]
        images_size = sum(os.path.getsize(os.path.join('images', f)) for f in image_files) / 1024 / 1024
        print(f"   📸 images文件夹: {len(image_files)} 个文件, {images_size:.1f} MB")
    else:
        print(f"   📸 images文件夹: 不存在")
    
    # 统计videos文件夹
    if os.path.exists('videos'):
        video_files = [f for f in os.listdir('videos') if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        videos_size = sum(os.path.getsize(os.path.join('videos', f)) for f in video_files) / 1024 / 1024
        print(f"   📹 videos文件夹: {len(video_files)} 个文件, {videos_size:.1f} MB")
    else:
        print(f"   📹 videos文件夹: 不存在")
    
    # 2. Excel状态统计
    try:
        df = pd.read_excel('merged_games.xlsx')
        print(f"\n📊 Excel下载状态统计:")
        print(f"   - 总游戏数: {len(df)}")
        
        # 图片下载状态
        if 'image_download_status' in df.columns:
            print(f"\n   🖼️  图片下载状态:")
            image_status = df['image_download_status'].value_counts()
            for status, count in image_status.items():
                if status:  # 只显示非空状态
                    percentage = count / len(df) * 100
                    print(f"     - {status}: {count} ({percentage:.1f}%)")
            
            # 统计成功和失败
            success_count = sum(count for status, count in image_status.items() if '成功' in str(status) or '已存在' in str(status))
            fail_count = sum(count for status, count in image_status.items() if '失败' in str(status))
            print(f"     总计: 成功 {success_count}, 失败 {fail_count}")
        
        # 视频下载状态
        if 'video_download_status' in df.columns:
            print(f"\n   📹 视频下载状态:")
            video_status = df['video_download_status'].value_counts()
            
            # 汇总统计
            success_count = sum(count for status, count in video_status.items() if '成功' in str(status))
            fail_count = sum(count for status, count in video_status.items() if '失败' in str(status))
            local_count = sum(count for status, count in video_status.items() if '本地文件' in str(status))
            other_count = len(df) - success_count - fail_count - local_count
            
            print(f"     - 下载成功: {success_count}")
            print(f"     - 下载失败: {fail_count}")
            print(f"     - 本地文件: {local_count}")
            print(f"     - 其他状态: {other_count}")
            
            # 显示主要失败原因
            if fail_count > 0:
                print(f"\n   主要失败原因:")
                fail_reasons = {}
                for status, count in video_status.items():
                    if '失败' in str(status):
                        if '404' in str(status):
                            fail_reasons['404 Not Found'] = fail_reasons.get('404 Not Found', 0) + count
                        elif '超时' in str(status):
                            fail_reasons['下载超时'] = fail_reasons.get('下载超时', 0) + count
                        else:
                            fail_reasons['其他错误'] = fail_reasons.get('其他错误', 0) + count
                
                for reason, count in fail_reasons.items():
                    print(f"     - {reason}: {count}")
    
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
    
    # 3. 下载效果分析
    print(f"\n📈 下载效果分析:")
    
    try:
        # 图片下载效果
        if 'image_download_status' in df.columns:
            total_images = len(df)
            successful_images = len(df[df['image_download_status'].str.contains('成功|已存在', na=False)])
            image_success_rate = successful_images / total_images * 100
            print(f"   📸 图片下载成功率: {image_success_rate:.1f}% ({successful_images}/{total_images})")
        
        # 视频下载效果
        if 'video_download_status' in df.columns:
            total_videos = len(df[df['video'].str.contains('https://', na=False)])  # 只统计在线视频
            successful_videos = len(df[df['video_download_status'].str.contains('成功', na=False)])
            if total_videos > 0:
                video_success_rate = successful_videos / total_videos * 100
                print(f"   📹 在线视频下载成功率: {video_success_rate:.1f}% ({successful_videos}/{total_videos})")
            else:
                print(f"   📹 无在线视频需要下载")
    
    except Exception as e:
        print(f"分析下载效果失败: {e}")
    
    # 4. 建议和总结
    print(f"\n💡 建议和总结:")
    print(f"   ✅ 图片下载效果良好，大部分图片URL有效")
    print(f"   ❌ 视频URL大多返回404错误，可能需要:")
    print(f"      - 检查视频URL格式是否正确")
    print(f"      - 确认视频服务器是否可访问")
    print(f"      - 考虑使用本地video文件夹中的视频")
    print(f"   📋 Excel文件已更新，包含详细的下载状态信息")
    
    # 5. 文件清单
    print(f"\n📄 生成的文件:")
    print(f"   - merged_games.xlsx (包含下载状态)")
    print(f"   - images/ (图片文件夹)")
    print(f"   - videos/ (视频文件夹)")
    print(f"   - download_media.py (下载程序)")
    print(f"   - monitor_download.py (监控程序)")

if __name__ == "__main__":
    generate_download_summary()
