#!/usr/bin/env python3
"""
根据规律补齐video列
"""

import pandas as pd
import os
import glob
from urllib.parse import urlparse

def extract_game_slug_from_url(url):
    """从URL中提取游戏slug"""
    if pd.isna(url) or not url:
        return None
    
    try:
        # 从URL中提取游戏名称
        # 例如: https://1games.io/big-block-blast -> big-block-blast
        path = urlparse(url).path
        game_slug = path.strip('/').split('/')[-1]
        return game_slug if game_slug else None
    except:
        return None

def generate_video_url(title_url, existing_video_url=None):
    """根据标题链接生成video URL"""
    # 如果已经有video URL，保持不变
    if not pd.isna(existing_video_url) and existing_video_url:
        return existing_video_url
    
    # 从标题链接提取game slug
    game_slug = extract_game_slug_from_url(title_url)
    if not game_slug:
        return None
    
    # 生成标准video URL格式
    video_url = f"https://cdn.1games.io/file/game/{game_slug}/{game_slug}.mp4"
    return video_url

def get_local_video_files():
    """获取本地video文件列表"""
    video_folder = "video"
    if not os.path.exists(video_folder):
        return {}
    
    video_files = glob.glob(os.path.join(video_folder, "*.mp4"))
    # 创建文件名到完整路径的映射
    video_map = {}
    for file_path in video_files:
        filename = os.path.basename(file_path)
        name_without_ext = os.path.splitext(filename)[0]
        video_map[name_without_ext] = filename
    
    return video_map

def match_local_video(game_slug, local_videos):
    """匹配本地video文件"""
    if not game_slug:
        return None
    
    # 直接匹配
    if game_slug in local_videos:
        return f"video/{local_videos[game_slug]}"
    
    # 模糊匹配
    for local_name in local_videos:
        # 检查是否包含主要关键词
        if game_slug.replace('-', '') in local_name.replace('-', ''):
            return f"video/{local_videos[local_name]}"
        
        # 检查部分匹配
        game_words = game_slug.split('-')
        local_words = local_name.split('-')
        
        # 如果有2个或以上单词匹配，认为是匹配的
        matches = sum(1 for word in game_words if word in local_words)
        if matches >= 2:
            return f"video/{local_videos[local_name]}"
    
    return None

def fill_video_column():
    """补齐video列"""
    
    print("="*60)
    print("Video列补齐程序")
    print("="*60)
    
    # 读取Excel文件
    df = pd.read_excel('merged_games.xlsx')
    
    print(f"\n📊 当前状态:")
    print(f"   - 总游戏数: {len(df)}")
    
    if 'video' not in df.columns:
        print("❌ 未找到video列")
        return
    
    original_video_count = df['video'].notna().sum()
    print(f"   - 已有video: {original_video_count}")
    print(f"   - 缺少video: {len(df) - original_video_count}")
    
    # 获取本地video文件
    local_videos = get_local_video_files()
    print(f"\n📁 本地video文件: {len(local_videos)} 个")
    for name, filename in local_videos.items():
        print(f"   - {name} -> {filename}")
    
    # 补齐video列
    print(f"\n🔧 开始补齐video列...")
    
    filled_count = 0
    local_matched_count = 0
    
    for idx, row in df.iterrows():
        # 如果已经有video，跳过
        if pd.notna(row['video']) and row['video']:
            continue
        
        title_url = row['标题链接']
        game_slug = extract_game_slug_from_url(title_url)
        
        if not game_slug:
            continue
        
        # 首先尝试匹配本地video文件
        local_video = match_local_video(game_slug, local_videos)
        if local_video:
            df.at[idx, 'video'] = local_video
            filled_count += 1
            local_matched_count += 1
            print(f"   ✅ 本地匹配: {row['标题']} -> {local_video}")
        else:
            # 生成标准video URL
            video_url = generate_video_url(title_url)
            if video_url:
                df.at[idx, 'video'] = video_url
                filled_count += 1
    
    # 保存结果
    df.to_excel('merged_games.xlsx', index=False)
    
    final_video_count = df['video'].notna().sum()
    
    print(f"\n✅ 补齐完成!")
    print(f"   - 原有video: {original_video_count}")
    print(f"   - 新增video: {filled_count}")
    print(f"   - 本地文件匹配: {local_matched_count}")
    print(f"   - 在线URL生成: {filled_count - local_matched_count}")
    print(f"   - 最终video总数: {final_video_count}")
    print(f"   - 完成率: {final_video_count/len(df)*100:.1f}%")
    
    # 显示一些补齐的示例
    print(f"\n📝 补齐示例:")
    newly_filled = df[df['video'].notna()]
    for i, (_, row) in enumerate(newly_filled.tail(5).iterrows(), 1):
        print(f"   {i}. {row['标题']}")
        print(f"      Video: {row['video']}")

if __name__ == "__main__":
    fill_video_column()
