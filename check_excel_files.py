#!/usr/bin/env python3
"""
检查两个Excel文件的结构
"""

import pandas as pd
import os

def check_excel_files():
    """检查Excel文件结构"""
    files = ['merged_games.xlsx', 'Soccer Physics(3).xlsx']
    
    for file in files:
        if os.path.exists(file):
            print(f'=== {file} ===')
            try:
                df = pd.read_excel(file)
                print(f'行数: {len(df)}')
                print(f'列数: {len(df.columns)}')
                print(f'列名: {list(df.columns)}')
                print(f'前3行数据:')
                for i in range(min(3, len(df))):
                    print(f'第{i+1}行:')
                    for col in df.columns:
                        print(f'  {col}: {df.iloc[i][col]}')
                    print()
                print()
            except Exception as e:
                print(f'读取失败: {e}')
        else:
            print(f'{file} 不存在')
        print('-' * 50)

if __name__ == "__main__":
    check_excel_files()
